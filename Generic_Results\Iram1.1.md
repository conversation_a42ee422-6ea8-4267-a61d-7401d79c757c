START pre-toc:Content Before Table of Contents

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: None
- Only Mentioned in Summary, Not Detailed: None
- No Data Found - Will Use General Response: Company Name (will use TENANT_NAME), DUNS Number (will state not available)
- No Data Found - Will State Not Available: None
- Duplication Check: Pass
- Sequence Check: Pass

---

**Section Title:** Content Before Table of Contents
**TOC Reference:** Appears in TOC: No

**Generated Content:**

**Proprietary Information Notice**

This proposal includes data that shall not be disclosed outside the Government and shall not be duplicated, used or disclosed in whole or in part for any purpose other than to evaluate this proposal. If, however, a Contract is awarded to this Offeror as a result of or in connection with the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting Contract. This restriction does not limit the Government’s right to use information contained in this data if it is obtained from another source without restriction. The data subject to the restriction is contained in all sheets of this proposal.
// Source: This proposal includes data that shall not be disclosed outside the Government and shall not be duplicated, used or disclosed in whole or in part for any purpose other than to evaluate this proposal. If, however, a Contract is awarded to this Offeror as a result of or in connection with the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting Contract. This restriction does not limit the Government’s right to use information contained in this data if it is obtained from another source without restriction. The data subject to the restriction is contained in all sheets of this proposal.

**Letter of Transmittal**

October 29, 2021

Solicitation Number: 2031JW22Q00022
Agency: Office of the Comptroller of the Currency (OCC)
Subject: TENANT_NAME Response to Cybersecurity Assessment and Compliance (CA&C)

TENANT_NAME is pleased to submit this comprehensive response to the Office of the Comptroller of the Currency (OCC) for the Cybersecurity Assessment and Compliance (CA&C) requirement, RFQ number 2031JW22Q00022. Our organization is driven by a commitment to solving complex challenges and ensuring the sustained success of our clients through exceptional project leadership across diverse industries. Our extensive past performance and strong reputation for client satisfaction underscore our consistent delivery of low-risk, high-quality, and value-added strategic support services.

TENANT_NAME possesses significant experience in providing Cybersecurity Assessment and Compliance services to a broad client base within both federal government and commercial sectors. Our past projects are similar in scope and complexity to the work outlined for the OCC program. Our deep subject matter expertise in Cybersecurity Assessment, Compliance, and our proven ability to deliver innovative services, coupled with robust program management skills, will ensure the highest quality of support for this critical initiative.

We are enthusiastic about the opportunity to collaborate with the OCC and are eager to demonstrate the immediate positive impact TENANT_NAME can bring to your organization. We acknowledge receipt of the RFQ and all subsequent Amendments, and we take no exception to the stated terms and conditions.

Our company information is as follows:

*   Company Name: TENANT_NAME
*   DUNS Number: Not provided in inputs

TENANT_NAME is personally committed to the success of this program and fully agrees with all terms, conditions, and provisions. We are prepared to furnish all items included in the solicitation. With our dynamic leadership, diligent supervision, and highly capable personnel, we are confident that TENANT_NAME is ideally suited to fulfill your needs. Should you have any questions or require additional information, please do not hesitate to contact our primary point of contact.

Respectfully,

[Signature Block - Placeholder for TENANT_NAME's President/Authorized Official]
[Name of President/Authorized Official]
President
// Source: Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter referred to as “Team Greenbrier”) are pleased to submit this response to the Office of the Comptroller of the Currency (OCC), Cybersecurity Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion for solving problems and ensuring the sustainability of results through project leadership extends across multiple industries. Team Greenbrier’s substantial past performance and reputation for excellence with our clients attest to the fact that we continually deliver low-risk, high-quality, value-added strategic support services. Team Greenbrier has been providing Cybersecurity Assessment and Compliance services for many years to a large base of clients in the federal government and commercial space as part of projects that are similar in size and scope to the work that will be part of the OCC program. Our deep subject matter expertise in Cybersecurity Assessment, Compliance, and experience in providing innovative services with proven program management skills will foster the support needed to ensure the best quality services. We are enthusiastic about the opportunity to work with you and eager to demonstrate immediately the positive impact we can have on your organization. Team Greenbrier acknowledges receipt of the RFQ and Amendments and takes no exception to the terms and conditions. Our company information includes: The information of teaming partner includes: +---------------------------------------+------------------------------+ | - Company Name: | - DUNS Number: | +=======================================+==============================+ We are personally committed to the success of this program and agree with all terms, conditions, & provisions and to furnish any or all items included in the solicitation. With our dynamic leadership, supervision, and more than capable personnel, we are confident that we are ideally suited to fulfill your needs. If you have any questions or need additional information, please contact our primary point of contact. Respectfully, [] Scotty Johnson PresidentEND pre-toc:Content Before Table of Contents



START toc:Table of Contents

**Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: None. The knowledge base provides content for sections *listed* in the Table of Contents (e.g., Past Performance), but not for the Table of Contents structure itself.
- Only Mentioned in Summary, Not Detailed: None. The RFP summary confirms the existence of sections like Key Personnel, which are listed in the Table of Contents, but does not provide content for the TOC structure.
- No Data Found - Will Use General Response: Not applicable for Table of Contents sections per instructions.
- No Data Found - Will State Not Available: Not applicable. The `section_outline` *is* the content for the Table of Contents.
- Duplication Check: Pass
- Sequence Check: Pass

---

**Section Title:** Table of Contents
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

We are pleased to present the comprehensive Table of Contents for our proposal in response to Solicitation Number 2031JW22Q00022 for Cybersecurity Assessment and Compliance (CA&C) services. This structured overview details the organization of our submission, ensuring clarity and ease of navigation for the evaluation team.

**Table of Contents**

1.  Assumptions
    1.1. Technical Assumptions
    1.2. Price Assumptions
2.  SF 1449
3.  Acknowledgment of Solicitation Amendments
4.  Completion of Representations
5.  Attestation
6.  Conflict of Interest Mitigation Plan
7.  GSA Federal Supply Schedule (FSS)
8.  Technical/Management Approach (No more than 15 pages)
    8.1. Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing
    8.2. Methodology for assigning risk ratings to weaknesses discovered during the assessment process
    8.3. Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)
    8.4. Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool
    8.5. Process for automating assessments
    8.6. Methodology for tracking, reporting and completing all work identified in the PWS
    8.7. Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section
9.  Key Personnel Resumes (No more than 3 pages per resume)
    9.1. Project Manager
    9.2. Cybersecurity Assurance & Compliance Program Support Lead
    9.3. Security & Privacy Control Assessor Lead
    9.4. Quality Assurance Lead
    9.5. Tier 2 eGRC Specialist
    9.6. Automated Assessment Developer
    9.7. IT Security Subject Matter Expert
10. Past PerformanceEND toc:Table of Contents



START 1-7:Sections 1-7: Administrative and Compliance Responses

**Section Title:** Sections 1-7: Administrative and Compliance Responses
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

**1 Assumptions**
This section typically outlines any key assumptions that underpin our proposed approach, pricing, or delivery schedule. Clearly articulating these assumptions ensures mutual understanding and helps manage expectations throughout the project lifecycle.
// Source: Generic proposal guidance

**1.1 Technical Assumptions**
Our technical approach is predicated on certain assumptions regarding the client's existing infrastructure, systems, and data availability. This subsection would detail any specific technical prerequisites or conditions that are assumed for the successful implementation and operation of our proposed solutions.
// Source: Generic proposal guidance

**1.2 Price Assumptions**
Our pricing structure is based on specific assumptions related to the scope of work, resource allocation, and any client-provided materials or support. This subsection would delineate any financial assumptions that directly impact the proposed cost, such as anticipated workload volumes or the availability of client personnel.
// Source: Generic proposal guidance

**2 SF 1449**
Content not available in the provided knowledge base. This section requires the completed and signed SF 1449 form, which is a specific solicitation document.

**3 Acknowledgment of Solicitation Amendments**
TENANT_NAME acknowledges receipt of the Request for Quotation (RFQ) and all associated Amendments. We confirm that we have reviewed all terms and conditions and take no exceptions.
// Source: Team Greenbrier acknowledges receipt of the RFQ and Amendments and takes no exception to the terms and conditions.

**4 Completion of Representations: OCC provision 1052.209-8001, Conflict of Interest Disclosure and Certification (Feb 2014)**
Content not available in the provided knowledge base. This section requires specific completion of the referenced OCC provision and certification.

**5 Attestation**
Content not available in the provided knowledge base. This section requires a specific attestation statement confirming that all quoted contractor personnel meet the Contractor Qualifications and Key Personnel requirements outlined in Section II of the Statement of Work.

**6 Conflict of Interest Mitigation Plan**
Content not available in the provided knowledge base. This section requires a description of any actual or potential conflicts of interest related to this requirement and, if identified, a corresponding mitigation plan. If no conflicts are identified, a written statement to that effect is required.

**7 GSA Federal Supply Schedule (FSS)**
Content not available in the provided knowledge base. This section requires explicit confirmation that all proposed services fall under TENANT_NAME's GSA Federal Supply Schedule (FSS), and if teaming arrangements are involved, identification of the teaming partner and their GSA FSS number for affected CLINs.

---
**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: None
- Only Mentioned in Summary, Not Detailed: 3 Acknowledgment of Solicitation Amendments
- No Data Found - Will Use Generic Response: 1 Assumptions, 1.1 Technical Assumptions, 1.2 Price Assumptions
- No Data Found - Will State Not Available: 2 SF 1449, 4 Completion of Representations, 5 Attestation, 6 Conflict of Interest Mitigation Plan, 7 GSA Federal Supply Schedule (FSS)
- Duplication Check: Pass
- Sequence Check: PassEND 1-7:Sections 1-7: Administrative and Compliance Responses



START 8:Technical/Management Approach

**Section Title:** Technical/Management Approach
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

TENANT_NAME's comprehensive technical and management approach is designed to ensure the successful execution of the Cybersecurity Assessment and Compliance (CA&C) program for the Office of the Comptroller of the Currency (OCC). Our methodology is rooted in proven processes, robust project management, and a deep understanding of cybersecurity best practices and regulatory compliance.

### 8.1 Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

TENANT_NAME employs a rigorous methodology for testing web applications, financial systems, network and infrastructure devices, and end-user devices, ensuring thorough security control assessments. Our process includes:

*   **Comprehensive Assessment Planning:** We develop and document a Security Assessment Plan (SAP) that precisely defines the scope of testing, identifies the specific controls to be evaluated, and outlines the execution methods. This plan details the appropriate security assessment level of effort, project management information, tasks, reviews, resources, and milestones for each system under assessment. // Source: ABC performed SCAs for VA’s information systems... Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the test.
*   **Detailed Test Procedure Development:** Our team identifies the controls to be tested, assesses their current status, and provides detailed test procedures. These procedures are adapted from established test banks and updated as necessary to reflect current system conditions. // Source: Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.
*   **Execution of Security Control Assessments (SCAs):** We execute the SAP, performing SCAs according to the defined processes and procedures. This includes assessing all software and hardware components, applying sequential, step-by-step assessment procedures for each test objective, and verifying configuration settings for all major IT products against secure benchmarks. // Source: ABC performed the SCA according to the processes and procedures described in the SAP. // Source: All software and hardware components assessed... Sequential, step-by-step assessment procedures for testing each test objective... Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed, location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark
*   **Reporting and Documentation:** We develop comprehensive Security Assessment Reports (SARs) that document each SCA, including assessment test objectives, test types (e.g., interview, examine, test), and the results of control assessment, evaluation, and analysis within the defined system boundary. // Source: ABC developed the Security Assessment Report to include the following: Documentation of each SCA... Assessment test objectives as identified in NIST SP 800-53A... Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A... Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment

### 8.2 Methodology for assigning risk ratings to weaknesses discovered during the assessment process

TENANT_NAME employs a clear methodology for assigning risk ratings to identified weaknesses, ensuring that vulnerabilities are appropriately prioritized and addressed. Our approach includes:

*   **Severity Assessment:** We assess the severity of weaknesses or deficiencies discovered within information systems and their operating environments. This assessment is based on prioritized levels reported by NIST. // Source: ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. // Source: ABC also provided an assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their environment of operation and recommend corrective actions to address identified vulnerabilities.
*   **Corrective Action Recommendations:** Following the assessment, we recommend specific corrective actions to address the identified vulnerabilities.
*   **Documentation of Findings:** All findings, including assigned risk ratings and recommended corrective actions, are meticulously documented, typically within a Plan of Action and Milestones (POA&M) import template. // Source: Results of Findings to be documented in the OCS POA&M Import Template (data defined file in Excel format)

### 8.3 Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

TENANT_NAME possesses experience in supporting audit-related activities and ensuring compliance with federal requirements. Our process for audit preparation and response includes:

*   **Compliance Oversight:** We ensure that security control objectives are continuously met to support federal regulatory requirements and the government oversight and audit community, including those related to annual Federal Information Systems Controls Audit Manual (FISCAM) audits. // Source: During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector General (OIG) has determined there is an IT Security Controls Material Weakness. // Source: This project ensures that the continued compliance and oversight of the security control objectives are met to support Federal regulatory requirements and the Federal Government oversight and audit community.
*   **Tracking and Reporting:** We provide visibility and reporting on the status and progress of activities, including Plan of Action and Milestones (POA&M) remediation and action items. We generate Action Item Update Reports to track the progress status of tasks. // Source: We Provide visibility i and reporting on, the status and progress of activities associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track progress status of VA wide tasks.

### 8.4 Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

TENANT_NAME effectively integrates the Risk Management Framework (RMF) Monitor step with eGRC tools to facilitate continuous monitoring and compliance. Our process involves:

*   **Artifact Management within GRC Tools:** We review and manage all required supporting artifacts within Governance, Risk, and Compliance (GRC) tools, aligning them with the security requirements captured in the NIST RMF. This ensures that all documentation necessary for assessment and authorization (A&A) is readily available and properly recorded. // Source: ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements captured in the NIST RMF... // Source: provided the customer the ability to lookup the ISO by name and facility, projects assigned, with links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other similar sources.
*   **RMF-Compliant Assessments:** We conduct Security Control Assessments (SCAs) in accordance with the Risk Management Framework (RMF), ensuring that all assessment activities align with RMF guidelines and policies. // Source: We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).
*   **Continuous Monitoring Planning and Execution:** We develop and implement Continuous Monitoring SCA Plans and Schedules, which outline the required activities and outputs as mandated by RMF Tasks. We then perform these continuous monitoring annual SCAs according to the established plan and schedule. // Source: ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks. // Source: We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.
*   **Communication and Reporting:** We ensure all required communications and reporting activities, as specified by RMF Tasks, are performed to maintain transparency and provide stakeholders with up-to-date information on the security posture. // Source: We performed all required communications and reporting activities as required by RMF Tasks.

### 8.5 Process for automating assessments

Content not available in the provided knowledge base. This was only referenced in high-level inputs.

### 8.6 Methodology for tracking, reporting and completing all work identified in the PWS

TENANT_NAME employs a robust methodology for tracking, reporting, and ensuring the completion of all work identified in the Performance Work Statement (PWS). Our approach includes:

*   **Project Oversight and Management:** We provide comprehensive project oversight, including prioritizing, planning, tracking, and reporting, to ensure the successful implementation of all projects and tasks. // Source: ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in order to ensure implementation of OIS FSS projects...
*   **Master Project Scheduling:** We develop and maintain an updated Master Project Schedule and timeline, detailing key components, milestones, and deliverables. This schedule is managed using tools like MS Project to track tasks and sub-elements. // Source: We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones and deliverables for ESO projects. // Source: We utilized MS Project to maintain the FSS Master Project Schedule of CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.
*   **Status Visibility and Reporting:** We provide continuous visibility and reporting on the status and progress of activities, including Plan of Action and Milestones (POA&M) remediation and action items. We generate various reports, such as Action Item Update Reports and Bi-Weekly Activity Reports (BWARs), detailing accomplishments, future activities, risks, and projected completion dates. // Source: We Provide visibility i and reporting on, the status and progress of activities associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track progress status of VA wide tasks. // Source: We created the BWAR using documents that detail current accomplishments/future activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format.
*   **Regular Communication:** We maintain consistent communication with leadership and project leads through weekly and ad-hoc communications, utilizing various virtual technologies and project management tools (e.g., MS Project, SharePoint, MS Excel, MS Word, Adobe Pro). This ensures timely updates on status, issues, risks, and accomplishments. // Source: We provided weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues, risks and accomplishments associated with FSS CRISP sustainment. // Source: We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks, and accomplishments associated with FSS CRISP sustainment. // Source: We also utilized existing project management tools (MS Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM.

### 8.7 Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

Content not available in the provided knowledge base. This was only referenced in high-level inputs.END 8:Technical/Management Approach



START 9:Key Personnel Resumes

**Section Title:** Key Personnel Resumes
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

TENANT_NAME understands the critical importance of presenting highly qualified and committed personnel for the successful execution of the Cybersecurity Assessment and Compliance (CA&C) program. As such, we are prepared to submit comprehensive resumes for each identified key personnel, demonstrating their relevant experience, certifications, and qualifications as outlined in the Statement of Work (SOW). Each resume will adhere to the specified length of no more than three pages. Furthermore, TENANT_NAME will provide a letter of commitment for each proposed key personnel, affirming their availability and dedication to this program.

### 9.1 Project Manager
Content not available in the provided knowledge base. This section would typically include the Project Manager's resume, detailing at least eight (8) years of experience in program and project management supporting information security or cybersecurity projects for the federal government. It would also confirm a bachelor's degree, Project Management Professional (PMP) or equivalent certification, hands-on experience with Microsoft Office Project, and experience with NIST Risk Management Framework, GRC tools (e.g., ServiceNow GRC, RSA Archer, CSAM, Xacta), and required certifications such as CISA, CAP, CISSP, or CISM.

### 9.2 Cybersecurity Assurance & Compliance Program Support Lead
Content not available in the provided knowledge base. This section would typically include the Cybersecurity Assurance & Compliance Program Support Lead's resume, demonstrating at least three (3) years of experience managing security compliance program support teams, six (6) years of experience developing RMF documentation, eight (8) years of Information Security experience, and two (2) years of experience using eGRC tools. It would also confirm relevant certifications such as CISSP, CISA, CAP, CRISC, or CISM, with preferred experience in ServiceNow GRC CAM.

### 9.3 Security & Privacy Control Assessor Lead
Content not available in the provided knowledge base. This section would typically include the Security & Privacy Control Assessor Lead's resume, showcasing three (3) years of experience managing security assessment teams, six (6) years of experience performing detailed, full-scope technical security control testing, and eight (8) years of Information Security experience. It would also detail two (2) years of experience with eGRC tools (preferably ServiceNow), experience with Qualys Enterprise, Archer, Nessus, HCL AppScan, and Kali Linux suite, advanced knowledge of security configurations, and certifications such as CISSP, CEH, CRISC, or CISA.

### 9.4 Quality Assurance Lead
Content not available in the provided knowledge base. This section would typically include the Quality Assurance Lead's resume, highlighting three (3) years of experience managing technical security QA teams/SA&A Package Independent Validation & Verification (IV&V), six (6) years of experience developing RMF documentation, six (6) years of experience conducting security and privacy control assessments, and eight (8) years of Information Security experience. It would also confirm two (2) years of experience with eGRC tools (preferably ServiceNow GRC tool suite, including CAM), and certifications such as CISSP, CRISC, or CISA.

### 9.5 Tier 2 eGRC Specialist
Content not available in the provided knowledge base. This section would typically include the Tier 2 eGRC Specialist's resume, detailing at least two (2) years of experience with developing/writing advanced custom ServiceNow script includes, user interface actions, user interface policies, access control lists, client scripts, scheduled jobs, data tables, and data fields. It would also demonstrate at least three (3) years of Integrated Risk Management (IRM)/GRC implementation experience (preferably GRC CAM experience and ServiceNow Certified Risk & Compliance implementation specialist), experience configuring and customizing ITSM suite, IT Operations Management suite, and NOW Platform Capabilities, and experience with technical components such as LDAP, Web Services, REST, SOAP, APIs, XML, and JavaScript. Additionally, it would confirm experience with ITILv3 Service Management processes and relevant administrator/developer and implementation specialist certifications.

### 9.6 Automated Assessment Developer
Content not available in the provided knowledge base. This section would typically include the Automated Assessment Developer's resume, demonstrating five (5) years of experience as a software developer and three (3) years of experience integrating cybersecurity tools (including Qualys, RSA Archer, and Splunk) with ServiceNow GRC. It would also confirm a Bachelor of Science degree in Computer Science.

### 9.7 IT Security Subject Matter Expert
Content not available in the provided knowledge base. This section would typically include the IT Security Subject Matter Expert's resume, showcasing ten (10) years of IT security experience and five (5) years of experience performing detailed, full-scope technical control testing. It would also confirm in-depth knowledge of and experience in implementing and using GRC platforms (preferably ServiceNow GRC tool suite, including CAM), hands-on experience with at least four of the specified technology products (Forescout CounterAct, ArcSight, HCL BigFix, Sailpoint, CyberArk, RES, and Splunk), extensive knowledge of security configurations, a Bachelor of Science degree in Computer Science, Cybersecurity, or Information Systems, and CISSP certification, with CCSP preferred.

---
**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: None
- Only Mentioned in Summary, Not Detailed: Key Personnel roles (names only, no resume content)
- No Data Found - Will Use General Response: N/A (This section requires specific, company-proprietary data)
- No Data Found - Will State Not Available: Key Personnel Resumes (all sub-sections)
- Duplication Check: Pass
- Sequence Check: PassEND 9:Key Personnel Resumes



START 10:Past Performance

**Section Title:** Past Performance
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

TENANT_NAME is committed to demonstrating our extensive experience and proven success in delivering services relevant to the Office of the Comptroller of the Currency (OCC) Cybersecurity Assessment and Compliance (CA&C) requirement. As requested, TENANT_NAME has facilitated the submission of three (3) Past Performance Questionnaire Surveys directly from our referenced customers to the OCC Contract Specialist. We understand that the OCC may also consider other relevant information, including data from the Contractor Performance Assessment Reporting System (CPAR), in evaluating our past performance. // Source: The Quoter shall request that references fill out and return three (3) Past Performance Questionnaire Survey (Attachment 3). Quoters may not submit the completed survey forms to the OCC Contract Specialist on behalf of their references; instead, the completed forms shall be returned directly by the referenced customers to the OCC Contract Specialist, per the information provided on the form. The OCC may also obtain and consider any other relevant information in evaluating past performance, including information contained in Contractor Performance Assessment Reporting System (CPAR).

Below, we highlight two examples of our relevant past performance that underscore our capabilities in cybersecurity assessment, compliance, and program management.

---

**Project 1: FSS Program Management Support**

**Contracting Agency / Business:** Department of Veterans Affairs
**Contract Name:** FSS Program Management Support (HMS Technologies)
**Contract Type:** FFP
**Place of Performance:** Remote
**Prime or Subcontractor:** Subcontractor

**Description of Services:**
TENANT_NAME provided comprehensive project oversight and support to the Department of Veterans Affairs (VA) Office of Information Security (OIS) Federal Supply Schedule (FSS) program, now known as ITOPS ESO. Our support encompassed prioritizing, planning, tracking, and reporting activities to ensure the successful implementation of OIS FSS projects, including SharePoint Support and Governance. A key aspect of our involvement was providing critical support to the Continuous Readiness Information Security Protection (CRISP) project. We were responsible for developing and maintaining an updated FSS Master Project Schedule and timeline, ensuring timely delivery of key components, milestones, and deliverables for ESO projects. Our team provided crucial visibility and reporting on the status and progress of CRISP sustainment activities, with a focus on existing security practices, Plan of Action and Milestones (POA&M) remediation, action item tracking, and standardization of training. We regularly furnished Action Item Update Reports, meticulously tracking the progress status of VA-wide tasks. // Source: ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness Information Security Protection (CRISP) project. We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track progress status of VA wide tasks.

**Specific Task Relevance:**
*   **FSS SharePoint Support:** TENANT_NAME conducted in-depth analysis of information within the SharePoint workload tracker to identify and prioritize areas for improvement in FSS business operations. This included recommending business process changes, applying pertinent technology like SharePoint tracking, and enhancing the ability to utilize SharePoint applications for improved business operations. We upgraded and enhanced the ISO Support Request Program, transforming it from a spreadsheet-based system into a more robust tool, enabling lookup of ISOs by name and facility, assigned projects, and linking to other data sources such as Governance, Risk, and Compliance (GRC) and VA Systems Inventory (VASI). We also delivered Tracking and Reporting SharePoint Pages for government approval. // Source: ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for improvements in FSS business operations methods to achieve improvements in FSS business operations which included: 1. Business process or procedures changes 2. Application of pertinent Technology (e.g., SharePoint tracking) 3. Ability to utilize SharePoint applications to improve business operations We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation) that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.
*   **FSS CRISP Program Support:** We developed and maintained an updated FSS Master Project Schedule and timeline for the CRISP project, utilizing MS Project to manage tasks and sub-elements. Our team coordinated project activities among regional directors, Network ISOs (NISOs), and other Subject Matter Experts (SMEs) related to CRISP. We provided continuous visibility and reporting on the status and progress of CRISP sustainment, focusing on security practices, POA&M remediation, and training standardization. We delivered Action Item Update Reports, tracking progress and compliance. Our communication efforts included weekly virtual meetings and ad-hoc communications using VA project management tools to report on status, issues, risks, and accomplishments. // Source: ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components, milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones. We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues, risks and accomplishments associated with FSS CRISP sustainment. We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks, and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any distribution.
*   **Governance Support:** TENANT_NAME provided ongoing program governance support to the FSS structured steering committee, including assessing and recommending project prioritization and operational tasks for improved demand and workload management. We assisted in creating a comprehensive FSS program strategy, mission, and goals, and continuously monitored performance metrics in graphical formats. Our expertise ensured that all resulting projects aligned with the FSS mission and goals. We also developed and maintained supporting templates, processes, and metrics, and facilitated training sessions to ensure proper program evaluation and realistic timelines. // Source: ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments, delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics were maintained and continuously revised, by us, as the need for change arises. We provided project governance management expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization, and present it to FSS leadership for review and approval. As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned with FSS mission and goals.
*   **FSS Requirements Elaboration Planning Support:** We assisted in updating the annual FSS Requirements Plan, ensuring it included the latest plan, delivery, and execution information. We maintained the plan quarterly, incorporating updates regarding OIS stakeholders and their interests. Our team collaborated with FSS leadership to manage, monitor, and communicate program status and goals. We also coordinated weekly status meetings for Bi-Weekly Activity Reports (BWARs), detailing accomplishments, future activities, risks, and projected completion dates. // Source: As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and approval from the COR/FSS VA PM. We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.
*   **FSS Requirements Plan Execution:** TENANT_NAME supported FSS management in overseeing requirements execution, developing various communications such as emails, memorandums, briefings, and presentations tailored to stakeholder needs. // Source: FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor these correspondence/communication products as needed.
*   **Medical Cyber Domain Support:** We provided ongoing communications and training support to FSS stakeholders, ensuring their understanding of threats to networked medical devices, patient care, mitigation strategies, and roles in implementing medical/special purpose device security. // Source: Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities in implementing medical/special purpose device security.
*   **FSS Incentive Program (FSSIP) Campaign Support:** TENANT_NAME collaborated with FSS leadership to suggest improvements for an effective FSSIP campaign, providing ideas and technical assistance to plan and update FSSIP information across various modalities. // Source: FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and Vanguard Magazine.
*   **FSS Communications Advisory Board (CAB) Support:** On a monthly basis, we collaborated with the CAB Chair and FSS Director to select meeting topics, facilitate in-house meetings, provide activity briefings, support topic research, capture notes, and conduct live meetings. // Source: FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, support topic research, capture notes, poll the group on topics, and conduct live meetings.
*   **FSS Executive Requirements And Ad Hoc Support:** We provided rapid-response communications support for priority security matters, including leadership messages, presentations, talking points, executive memorandums, bulletins, and ad hoc training on new security initiatives. // Source: FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and ad hoc training on new security initiatives.

---

**Project 2: Security Control Assessments**

**Contracting Agency / Business:** Department of Veterans Affairs
**Contract Name:** Security Control Assessments
**Contract Type:** Firm Fixed Price (FFP)
**Place of Performance:** Remote Locations
**Prime or Subcontractor:** [Not specified in knowledge base]
**Period of Performance:** 12 months from date of award, with four 12-month option periods.

**Background:**
The Department of Veterans Affairs (VA) Office of Information & Technology (OI&T), Office of Information Security (OIS), relies on robust Information Management/Information Technology (IM/IT) systems to deliver benefits and services to Veterans. Annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audits (CFSA) by the VA Office of Inspector General (OIG) have identified IT Security Controls Material Weaknesses in areas such as security management, access controls, configuration management, and contingency planning, including unpatched vulnerabilities in databases, servers, and network devices. This project was critical to ensuring continuous compliance and oversight of security control objectives, supporting federal regulatory requirements and government oversight. // Source: The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals. During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls, configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support Federal regulatory requirements and the Federal Government oversight and audit community.

**Scope of Work:**
TENANT_NAME performed comprehensive Security Control Assessments (SCAs) to determine the extent to which required NIST information security controls were correctly implemented, operating as intended, and producing desired outcomes within VA’s information systems. Our work included: // Source: ABC performed: 1. Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting security requirements.
*   **Security Assessment Plan (SAP) Development:** We developed and documented detailed Security Assessment Plans (SAPs) outlining the scope of testing, controls to be tested, and execution methods. // Source: a) Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the test.
*   **SAP Execution:** Our team executed the developed SAPs efficiently and effectively. // Source: b) Execute the SAP.
*   **Security Assessment Report (SAR) Preparation:** We prepared comprehensive Security Assessment Reports (SARs) documenting issues, findings, and recommendations from the SCAs. // Source: c) Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).
*   **Control Effectiveness Assessment:** We assessed a selected subset of technical, management, and operational security controls within VA’s information systems, determining their overall effectiveness in accordance with the organization-defined monitoring strategy. This included assessing the severity of discovered weaknesses and recommending corrective actions. // Source: 2. Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.

**Specific Task Relevance:**
*   **Contractor Independent Assessment:** TENANT_NAME performed SCAs for approximately 375 VA information systems within VA’s accreditation boundaries, including FIPS High, FIPS Moderate, and unclassified systems. Given that SCAs expire every three years, we assessed approximately 125 systems annually. We provided critical assessments of the criticality of each weakness or deficiency based on NIST prioritized levels and recommended corrective actions. Our methodologies ensured all SCA tests were developed, executed, and reported consistently with VA Policy, VA’s Assessment and Authorization (A&A) Standard Operating Procedure (SOP), and SCA Guidelines, adhering to the Risk Management Framework (RMF). // Source: ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their environment of operation and recommend corrective actions to address identified vulnerabilities. ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).
*   **Security Control Assessment Preparation:** We reviewed all required supporting artifacts in the Governance, Risk, and Compliance tool, collaborating with System Owners (SOs) to develop System Rules of Engagement (ROE) Agreements. These ROEs accurately identified the scope of testing, network ranges, system components, locations, assessment type and method, tools used, and a detailed list of required artifacts. We developed and submitted SCA Test Plans, identifying assessment levels of effort, project management information, key resources, roles, personnel, and anticipated schedules. Our plans listed controls to be tested, their current status, and test procedures from the OCS Test Bank. We also developed and documented RMF SAPs, ensuring comprehensive descriptions of all processes and procedures. // Source: ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed. Security Control Assessment Preparation - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to develop a System Rules of Engagement (ROE) Agreement. The ROE must correctly identify the following: 1) Scope of testing a) ABC only tested for controls within scope of system type, location, and within the appropriate management control 2) Network ranges being assessed 3) System components being assessed 4) Locations being assessed 5) SCA and all members conducting assessments including systems being used 6) Assessment type and method 7) Tools used for the assessment 8) A detailed list of artifacts or evidence not of record with the system and needed to support the SCA - ABC developed and submitted a SCA Test Plan which: 1. Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance reviews), resources, and milestones for the system being tested. 2. Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities. 3. Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA. 4. Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions. 5. Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all necessary parties. - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security categorization.
*   **Security Control Assessment Execution:** TENANT_NAME performed SCAs strictly according to the processes and procedures described in the SAP. We conducted SCA Pre-Site Meetings, provided daily status updates on system component assessments, submitted Weekly Status Reports, and held SCA Out-Brief Meetings. // Source: ABC performed the SCA according to the processes and procedures described in the SAP. - ABC completed the following communication and reporting activities: 1. SCA Pre-Site Meeting 2. System Component Assessment Daily Status 3. Weekly Status Report 4. SCA Out-Brief Meeting
*   **Security Assessment Report (SAR) Development:** We developed comprehensive SARs that included documentation of each SCA, assessment test objectives (NIST SP 800-53A), test types, assessed software and hardware components, sequential assessment procedures, and results of control assessment, evaluation, and analysis. Our reports provided evidence that all components were tested or covered by representative samples, rationale for untested systems, and confirmation of configuration settings. We determined whether security controls were "Satisfied" or "Other Than Satisfied," documented findings in the OCS POA&M Import Template, and provided unbiased, factual results. We also identified and explained all artifacts used in the assessment. // Source: ABC developed the Security Assessment Report to include the following: 1. Documentation of each SCA 2. Assessment test objectives as identified in NIST SP 800-53A 3. Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A 4. All software and hardware components assessed 5. Sequential, step-by-step assessment procedures for testing each test objective 6. Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment 7. Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices 8. Rationale for any system or device in the inventory not directly tested 9. Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed, location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or “Other Than Satisfied” determination 11. Results of Findings to be documented in the OCS POA&M Import Template (data defined file in Excel format) 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual results for each system component type 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)
*   **Lessons Learned Integration:** We developed and updated Lessons Learned from A&A activities, incorporating them into processes and procedures as applicable, with approval from the VA Project Manager. Feedback was collected from various stakeholders including the AO, SO, ISSO, IT System Manager, Security Controls Assessor, VA Program Manager, COR, and CO. // Source: ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned were collected from the following individuals: 1. AO 2. SO 3. ISSO 4. IT System Manager/System Steward 5. Security Controls Assessor 6. VA Program Manager 7. COR 8. CO
*   **Ongoing Security Control Assessments:** TENANT_NAME developed a Continuous Monitoring SCA Plan and Schedule, including required activities and outputs per RMF Tasks. We performed Continuous Monitoring Annual SCAs according to this plan and conducted all required communications and reporting activities as mandated by RMF Tasks. // Source: ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule. - We performed all required communications and reporting activities as required by RMF TasksEND 10:Past Performance



