docstring_parser-0.16.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
docstring_parser-0.16.dist-info/LICENSE.md,sha256=3-UUozeuhBer0xqK9we71hcrA-VDC7CD4UWJnql6Puo,1084
docstring_parser-0.16.dist-info/METADATA,sha256=1EnPE7nuxzfewBCp7KU_-Dw9v9g8mNwHSLhGeIsHZv0,3031
docstring_parser-0.16.dist-info/RECORD,,
docstring_parser-0.16.dist-info/WHEEL,sha256=sP946D7jFCHeNz5Iq4fL4Lu-PrWrFsgfLXbbkciIZwg,88
docstring_parser/__init__.py,sha256=78WKoStdo7zvCcEU1Sm2zLvU_BRGKyTYjlScFEB14nk,695
docstring_parser/__pycache__/__init__.cpython-312.pyc,,
docstring_parser/__pycache__/attrdoc.cpython-312.pyc,,
docstring_parser/__pycache__/common.cpython-312.pyc,,
docstring_parser/__pycache__/epydoc.cpython-312.pyc,,
docstring_parser/__pycache__/google.cpython-312.pyc,,
docstring_parser/__pycache__/numpydoc.cpython-312.pyc,,
docstring_parser/__pycache__/parser.cpython-312.pyc,,
docstring_parser/__pycache__/rest.cpython-312.pyc,,
docstring_parser/__pycache__/util.cpython-312.pyc,,
docstring_parser/attrdoc.py,sha256=ZYMM5BIULr7FzZVHx-kBMLpG5Q3YKSzbBBJg5qk3cwk,4440
docstring_parser/common.py,sha256=isfQPfuz4Kor4dxF4MXpju52SNmbvE0rd44n4PX2Y4o,6319
docstring_parser/epydoc.py,sha256=kv62jQGMPkNtzPYgwu_vbXApIsGDsPIyTcuBsNYEfz8,8926
docstring_parser/google.py,sha256=ZXPPJYRljl_uj1YqD6ZW_KZj_1AjmkdGP4iZqNkJhMs,13308
docstring_parser/numpydoc.py,sha256=t9K4tYKN1RADiOS9TDUyrZy821lLrptaIHuahqEDeJE,15983
docstring_parser/parser.py,sha256=AB-R7X36siUZ1rqRQC3jtZKgAQJnkUGdPDjRJb54y9k,2938
docstring_parser/py.typed,sha256=bWew9mHgMy8LqMu7RuqQXFXLBxh2CRx0dUbSx-3wE48,27
docstring_parser/rest.py,sha256=FIOAEQUFW8z77RoF5oXfOJFI_LhsI_FuUAdLgWw5lGs,8291
docstring_parser/tests/__init__.py,sha256=6VULghkufHkqtZfyiBamwZQNBA3z7f4TMEcBclUqTKE,34
docstring_parser/tests/__pycache__/__init__.cpython-312.pyc,,
docstring_parser/tests/__pycache__/_pydoctor.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_epydoc.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_google.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_numpydoc.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_parse_from_object.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_parser.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_rest.cpython-312.pyc,,
docstring_parser/tests/__pycache__/test_util.cpython-312.pyc,,
docstring_parser/tests/_pydoctor.py,sha256=nosAlcm_ovpetXTWnrLewqnP4Ea7pM_xbABAspBivcc,770
docstring_parser/tests/test_epydoc.py,sha256=wpcgNhTEObYRN1x628POKgUj2q7nPVlDMJHtlISpZ3o,19096
docstring_parser/tests/test_google.py,sha256=cNCInGobpinrGGsDzZKd1v0fPaVJ_t7iIMFjB0yrZzM,26577
docstring_parser/tests/test_numpydoc.py,sha256=KWgkiBEJ54NzkPMgeIlATju2idDR_I5FPltKGDZlXTU,28987
docstring_parser/tests/test_parse_from_object.py,sha256=n6zM9z7xSOjCl8Y9IDKWFul6Kk06XgNwfFVHzvK46xk,3823
docstring_parser/tests/test_parser.py,sha256=mZ2PeTxtHmVacyLLMszEduuEJ6691c9WHvqpr36q59Y,6699
docstring_parser/tests/test_rest.py,sha256=YjPahfB4QUqtGXe2rEFxzxzx7S8xaBrsRykWm7ZKGXQ,15194
docstring_parser/tests/test_util.py,sha256=rN8vMUKfyMmKdDxKV6RrjPXPPQclmX7wcN6aSrj4NFI,1739
docstring_parser/util.py,sha256=SQ9IKj70j9jVfNDeFKUFWcK73oxGSfeE1mRsB6ReU10,4507
