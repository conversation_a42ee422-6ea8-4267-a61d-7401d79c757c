def get_outline_breakdown_prompt(outline):
    prompt = f"""Role: Intelligent Outline Breakdown Specialist

Objective:
- Transform the provided outline into a structured JSON representation with complete content
- Optimize section breakdown for proposal writing workflow
- Ensure each section is self-sufficient and independent
- Include the exact content from the outline under each section

Breakdown Principles:
1. Section Consolidation Strategy:
   - Identify and preserve any content before the Table of Contents (TOC) as a separate section
   - Include the Table of Contents (TOC) itself as a distinct section
   - Use the TOC as the primary guide for identifying major sections and their subsections
   - Combine small, related sections (3-4 consecutive sections with minimal subsections)
   - Break down large, complex sections with multiple substantive subsections
   - Maintain original outline structure and hierarchy
   - Create sections that can be written independently

2. JSON Output Requirements:
   - Capture exact section and subsection titles
   - Include the complete original content under each subsection
   - Number sections sequentially
   - Group related small sections
   - Ensure each section is comprehensive yet manageable
   - Include "Pre-TOC Content" section if content exists before the TOC
   - Include "Table of Contents" section with the original TOC content

3. Content Preservation Guidelines:
   - Copy the exact content from the outline verbatim under each subsection
   - Include all instructions, requirements, deliverables, and specifications
   - Preserve formatting, bullet points, and detailed descriptions
   - Do not summarize or paraphrase - copy the complete original text
   - Maintain all technical details and specific requirements

4. Processing Guidelines:
  - Identify and separate content that appears before the Table of Contents
   - Preserve the Table of Contents as its own section
   - Ignore administrative elements (page numbers, headers, footers)
   - Focus on substantive content sections
   - Preserve original section order and relationships
   - Create logical, coherent section groups

5. Writing Workflow Considerations:
   - Enable proposal writers to tackle sections in multiple writing sessions
   - Make each section a self-contained writing unit with complete content
   - Allow flexibility in writing sequence while maintaining document flow

Specific Handling Rules:
- Always check for and preserve content that appears before the Table of Contents
- Always include the Table of Contents itself as a separate section in the JSON output
- If a section has multiple complex subsections, break it into separate entries
- For sections with minimal content, combine them into logical groups
- Maintain the semantic integrity of the original outline
- Include ALL content that appears under each section/subsection in the original outline

Input Outline:
{outline}


Example JSON: REMEMBER THAT IT IS COMPULSORY TO FOLLOW THIS JSON LAYOUT ALWAYS.

Example JSON: REMEMBER THAT IT IS COMPULSORY TO FOLLOW THIS JSON LAYOUT ALWAYS.

{{
  "sections": [
    {{
      "section_number": "pre-toc",
      "section_title": "Content Before Table of Contents",
      "subsections": [
        {{
          "subsection_title": "Cover Page",
          "content": "Complete original content that appears before the TOC including all text exactly as written in the source outline."
        }}
      ]
    }},
    {{
      "section_number": "toc",
      "section_title": "Table of Contents",
      "subsections": [
        {{
          "subsection_title": "Table of Contents",
          "content": "Complete original TOC content exactly as written in the source outline."
        }}
      ]
    }},
    {{
      "section_number": "1-7",
      "section_title": "Sections 1-7: Administrative and Compliance Responses",
      "subsections": [
        {{
          "subsection_title": "1 Assumptions",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "  1.1 Technical Assumptions",
          "content": "Complete original content from the outline for this subsection including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "  1.2 Price Assumptions",
          "content": "Complete original content from the outline for this subsection including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "2 SF 1449",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "3 Acknowledgment of Solicitation Amendments",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "4 Completion of Representations",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "5 Attestation",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "6 Conflict of Interest Mitigation Plan",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "7 GSA Federal Supply Schedule (FSS)",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }}
      ]
    }}
  ]
}}

Additional Guidance:
- Prioritize clarity and writability
- Ensure comprehensive coverage
- Maintain the document's original intent and structure
- If there is no subsection available for a section, then include the section_title as a subsection_title
- CRITICAL: Include the complete, exact content from the original outline under each subsection's "content" field
- Do not summarize, paraphrase, or modify the original content - copy it verbatim
- Include all technical specifications, requirements, deliverables, and instructions exactly as they appear
- Do not add additional text/commentary at the beginning or end of the response e.g., Here is the response ... or Okay, I will now generate the content or other unwanted/unrelevant commentary.
"""
    return prompt
  
# def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
# Mode: You must act as both **validator and generator**.

# You are drafting ONE specific RFP response section using the inputs provided.  
# Before you generate anything, **analyze** each item in the `section_outline` to determine:

# ---

# ## Phase 1: Agentic Validation

# For each required section component:
# 1. Check `knowledge_base`:  
#    - Is there clear, detailed, directly relevant content?
#    - If yes →  mark as usable.
#    - If partially relevant or too short → use what exists only.
#    - If not available →  categorize for response strategy (see Phase 2).

# 2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
#    - Note:
#      `"No detailed content found. This was only referenced in high-level inputs."`
#    - Then apply response strategy based on content type.

# 3. **When using content from `section_outline`:**
#    - Use outline for structure and requirements understanding
#    - REPHRASE outline content into professional proposal language
#    - Do not copy outline text verbatim
#    - Transform requirements into capability statements

# 4. Track:
#    - Items with no data
#    - Items mentioned only in summaries
#    - Exact matches with usable content

# ---

# ## Phase 2: Final Response Generation

# **Content Strategy Based on Type:**

# **FIRST PRIORITY - Use available content when it exists:**
# - If knowledge_base contains relevant content → USE IT with proper source citation
# - If rfp_summary contains relevant content → USE IT with proper source citation
# - Always prioritize actual content over "Content not available" statements

# **ONLY state "Content not available in the provided knowledge base" when:**
# - No relevant content exists in knowledge_base OR rfp_summary AND
# - The section involves:
#   * Past performance examples
#   * Corporate experience details
#   * Solicitation-specific details, terminology, systems
#   * Specific numbers, thresholds, requirements
#   * Company-specific capabilities, certifications, personnel
# **Note: Do NOT add source citations for "Content not available" statements.**

# **FOR sections with no content available in knowledge_base (except the above exceptions), provide generic descriptions:**
# - Add a brief generic description of what the section should typically cover
# - This generation should be independent of outline, summary, past performance
# - Focus on universal proposal elements that apply across all RFPs
# - Use language like "This section typically covers..." or "Standard practice includes..."
# - Add source tag: "// Source: Generic proposal guidance"
# - **Exception: Do NOT provide generic descriptions for Table of Contents (TOC) sections**
# - **Note: Provide generic content even if outline guidance exists - outline is only for structure**

# **Note: The "Industry best practices" source tag is now redundant and should not be used. Use "Generic proposal guidance" instead for all generic content.**

# Now generate the full section **ONLY after validation** is complete.

# **Rules for Response:**
# - Follow `section_outline` exactly.
# - Apply content strategy based on validation in Phase 1.
# - Use "we" or "TENANT_NAME" tone (professional + persuasive).
# - Preserve order from `knowledge_base` unless chronology is stated.
# - **When using content from outline: REPHRASE it, do not copy verbatim. Transform outline language into professional proposal language.**
# - Add `// Source: [Exact content snippet]` ONLY when using actual content from knowledge_base or rfp_summary.
# - Add `// Source: Generic proposal guidance` when providing generic descriptions for sections with no knowledge_base content.
# - For "Content not available" statements, do NOT add any source citation.
# - **Note: Do NOT use "RFP Outline" or "Industry best practices" source tags - use outline only for structure**
# - **Decision logic for content generation:**
#   * **STEP 1:** Check if relevant content exists in knowledge_base or rfp_summary → USE IT with proper source citation (highest priority)
#   * **STEP 2:** If no content available AND section involves past performance, corporate experience, RFP-specific details → state "Content not available"
#   * **STEP 3:** If no content available AND section is Table of Contents (TOC) → state "Content not available" (no generic description)
#   * **STEP 4:** If no content available AND section is any other general proposal element → provide generic description → cite as "// Source: Generic proposal guidance"
#   * **STEP 5:** If unsure → default to "Content not available"
#   * **Note: Use outline only for structure and organization, not as content source**

# ## Output Format:

# **Section Title:** {current_section_outline}  
# **TOC Reference:** Appears in TOC: Yes/No  

# **Generated Content:**  
# [Structured, source-tagged content]

# ---

# **HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
# - Direct Matches from Knowledge Base: [List]
# - Only Mentioned in Summary, Not Detailed: [List]
# - No Data Found - Will Use General Response: [List]
# - No Data Found - Will State Not Available: [List]
# - Duplication Check: Pass/Fail  
# - Sequence Check: Pass/Fail

# ---

# Inputs:
# - RFP Outline: {full_outline}  
# - Section Outline: {current_section_outline}  
# - RFP Summary: {procurement_summary}  
# - Knowledge Base: {past_performance}
# """
#     return prompt

 # ===============================actual v6 prompt===========================================
def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
    prompt = f"""
Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** {current_section_outline}  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: {full_outline}  
- Section Outline: {current_section_outline}  
- RFP Summary: {procurement_summary}  
- Knowledge Base: {past_performance}
"""
    return prompt


# ===============================POST-PROCESSING FOR CONTENT NOT AVAILABLE===========================================

def get_content_enhancement_prompt():
    """
    Prompt for enhancing sections that have "Content not available in the provided knowledge base"
    with generic proposal content.
    """
    prompt = """
Role: Federal Proposal Content Enhancement Specialist

You are tasked with enhancing a proposal response by replacing "Content not available in the provided knowledge base" statements with appropriate generic content.

## CRITICAL RULES:

### What TO Replace with Generic Content:
- General methodology descriptions
- Standard processes and procedures
- Universal compliance approaches
- Industry-standard frameworks
- Common project management practices
- Quality assurance processes
- Risk management strategies
- Communication protocols
- Training and development approaches
- Standard deliverable formats
- Typical organizational structures

### What NOT TO Replace (Keep "Content not available"):
- Past performance examples
- Corporate experience details
- Company-specific names, addresses, contact details
- Solicitation-specific numbers, thresholds, requirements
- Company-specific capabilities, certifications, personnel
- Contract numbers, dollar amounts, dates
- Client names or specific organizations
- Proprietary methodologies or tools
- NAICS codes, DUNS numbers, registration details
- Specific personnel names or resumes
- Table of Contents sections

## ENHANCEMENT APPROACH:

1. **Identify Sections to Enhance**: Look for "Content not available in the provided knowledge base" statements that fall into the "What TO Replace" category.

2. **Generate Generic Content**: Replace with factual, industry-standard information that any qualified federal contractor would typically provide. Use language like:
   - "This section typically covers..."
   - "Standard practice includes..."
   - "Our approach generally involves..."
   - "Industry best practices suggest..."

3. **Maintain Professional Tone**: Use the same professional, proposal-focused tone as the rest of the document.

4. **Keep Structure**: Maintain all existing headings, formatting, and section structure.

5. **Add Source Tag**: For enhanced content, add: `// Source: Generic proposal guidance`

## INSTRUCTIONS:

1. Read through the entire proposal response
2. Identify all "Content not available in the provided knowledge base" statements
3. For each statement, determine if it should be enhanced or kept as-is based on the rules above
4. Replace appropriate statements with generic, professional content
5. Keep all other content exactly as provided
6. Maintain the same formatting and structure

## OUTPUT:
Return the complete enhanced proposal with appropriate "Content not available" statements replaced with generic content and proper source attribution.

---

**PROPOSAL TO ENHANCE:**

{proposal_content}

---

**ENHANCED PROPOSAL:**
"""
    return prompt


def enhance_proposal_content(combined_response, client):
    """
    Process the combined response to enhance "Content not available" sections
    with generic proposal content where appropriate.

    Args:
        combined_response (str): The complete proposal response from the notebook
        client: The LLM client for making API calls

    Returns:
        str: Enhanced proposal with generic content filled in
    """
    print("Starting content enhancement process...")

    # Check if there are any "Content not available" statements
    if "Content not available in the provided knowledge base" not in combined_response:
        print("No 'Content not available' statements found. Returning original response.")
        return combined_response

    # Generate enhancement prompt
    enhancement_prompt = get_content_enhancement_prompt()
    full_prompt = enhancement_prompt.format(proposal_content=combined_response)

    print("Sending enhancement request to LLM...")

    try:
        # Call LLM for enhancement
        response = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=8000,
            temperature=0.3,
            messages=[{"role": "user", "content": full_prompt}]
        )

        enhanced_content = response.content[0].text if hasattr(response.content[0], 'text') else str(response.content[0])

        # Extract just the enhanced proposal content
        if "**ENHANCED PROPOSAL:**" in enhanced_content:
            enhanced_content = enhanced_content.split("**ENHANCED PROPOSAL:**")[1].strip()

        print("Content enhancement completed successfully!")
        return enhanced_content

    except Exception as e:
        print(f"Error during content enhancement: {e}")
        print("Returning original response.")
        return combined_response
# #===================treat outline as valid content======== 
# def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
# Mode: Act as both **validator and generator**.

# You're drafting one specific RFP response section using the provided inputs. First, **validate** the availability of relevant content before generating anything.

# ---

# ## Phase 1: Agentic Validation

# For each component in the `section_outline`:

# 1. **Knowledge Base**:  
#    - If clear, detailed, relevant → Use it.  
#    - If partial or minimal → Use cautiously.  
#    - If not available → Skip unless eligible for generalization.

# 2. **RFP Summary**:  
#    - If referenced here but not detailed in Knowledge Base:  
#      - Add a **brief mention** with this note:  
#        `"No detailed content found. This was only referenced in high-level inputs."`

# 3. **Section Outline**:  
#    - Use for structure and phrasing.  
#    - Do NOT label content as generic if it is derived from section outline phrasing or structure.

# 4. **If content is not available in any inputs, but it is a universal expectation (e.g., QA processes, training, program management practices):**  
#    - You may generate generic content only if:  
#      - It does **not** involve past performance, company-specific experience, or solicitation-specific data.  
#      - It reflects **widely accepted best practices** or factual information.  
#    - In such cases, label it with:  
#      `[This section is based on factual information and general practices.]`

#    ❗️**Important**: If a phrase or content is derived directly from the Section Outline (structure, bullets, or wording), you must NOT label it as general content. Outline-derived content is treated as a valid input.

# 5. **Do NOT fabricate or generalize content for the following categories**:  
#    - Past performance  
#    - Company-specific capabilities  
#    - Project history  
#    - Solicitation-specific terminology, systems, numbers, thresholds  

#    If content for these is unavailable, clearly state:  
#    `"Content not available in the provided knowledge base."`

# ---

# ## Phase 2: Final Response Generation

# Generate only after validation.

# **Rules for Generation**:  
# - Follow the `section_outline` order.  
# - Use validated inputs only (Knowledge Base, RFP Summary, or Outline phrasing).  
# - Maintain a professional, persuasive tone using "we" or "TENANT_NAME".  
# - Add `// Source: [Exact content snippet]` after each subsection.  
# - If skipping a section due to no data, state:  
#   `"Content not available in the provided knowledge base."`  
# - If using LLM-generated factual content (based on general practices), label it with:  
#   `[This section is based on factual information and general practices.]`  
# - NEVER use the label [This section is based on factual information and general practices.] for any content inferred or directly structured from the Section Outline.

# The Section Outline is always treated as a valid structural input, even if its phrasing is broad or generic.

# Only use the label when the content is entirely generated by the LLM without any linkage to the outline, knowledge base, or summary.

# ---

# ## Output Format:

# **Section Title:** {current_section_outline}  
# **TOC Reference:** Appears in TOC: Yes/No  

# **Generated Content:**  
# [Full content here, with appropriate labels and sources]

# ---

# **HELP TEXT: Agentic Validation Summary:**  
# - Direct Matches from Knowledge Base: [List]  
# - Only Mentioned in Summary, Not Detailed: [List]  
# - Generic Content Substituted For: [List]  
# - Skipped Due to Missing Data: [List]  
# - Duplication Check: Pass/Fail  
# - Sequence Check: Pass/Fail

# ---

# Inputs:  
# - RFP Outline: {full_outline}  
# - Section Outline: {current_section_outline}  
# - RFP Summary: {procurement_summary}  
# - Knowledge Base: {past_performance}
# """
#     return prompt

   #==============================prompt v6 with generic part Summary as sometimes valid, kb as valid and outline for structure======================
# def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
# Mode: Act as both **validator and generator**.

# You're drafting one specific RFP response section using the provided inputs. First, **validate** the availability of relevant content before generating anything.

# ---

# ## Phase 1: Agentic Validation

# For each component in the `section_outline`:

# 1. **Knowledge Base**:  
#    - If clear, detailed, relevant → Use it.  
#    - If partial or minimal → Use cautiously.  
#    - If not available → Skip unless eligible for generalization.

# 2. **RFP Summary**:  
#    - If referenced here but not detailed in Knowledge Base:  
#      - Add a **brief mention** with this note:  
#        `"No detailed content found. This was only referenced in high-level inputs."`

# 3. **Section Outline**:  
#    - Use for structure only.  
#    - Do not label content as generic if inferred from outline phrasing.

# 4. **If content is not available in any inputs, but it is a universal expectation (e.g., QA processes)**:  
#    - You may generate generic content only if:  
#      - It doesn’t involve past performance, company-specific experience, or solicitation-specific data.  
#      - It reflects widely accepted best practices.  
#    - In such cases, label it with:  
#      `[This section is based on factual information and general practices.]`

# 5. **Do NOT fabricate or generalize content related to**:  
#    - Past performance  
#    - Company-specific capabilities  
#    - Project history  
#    - Solicitation-specific terminology, systems, numbers, thresholds  
#    If unavailable, clearly state:  
#    `"Content not available in the provided knowledge base."`

# ---

# ## Phase 2: Final Response Generation

# Generate only after validation.

# **Rules for Generation**:  
# - Follow the `section_outline` order.  
# - Use validated inputs only (Knowledge Base, RFP Summary, or Outline phrasing).  
# - Maintain a professional, persuasive tone using "we" or "TENANT_NAME".  
# - Add `// Source: [Exact content snippet]` after each subsection.  
# - If skipping a section due to no data, state:  
#   `"Content not available in the provided knowledge base."`  
# - If using LLM-generated factual content, add:  
#   `[This section is based on factual information and general practices.]`  
# - Do **NOT** use this label for content derived from any valid input (Knowledge Base, Summary, Outline).

# ---

# ## Output Format:

# **Section Title:** {current_section_outline}  
# **TOC Reference:** Appears in TOC: Yes/No  

# **Generated Content:**  
# [Full content here, with appropriate labels and sources]

# ---

# **HELP TEXT: Agentic Validation Summary:**  
# - Direct Matches from Knowledge Base: [List]  
# - Only Mentioned in Summary, Not Detailed: [List]  
# - Generic Content Substituted For: [List]  
# - Skipped Due to Missing Data: [List]  
# - Duplication Check: Pass/Fail  
# - Sequence Check: Pass/Fail

# ---

# Inputs:  
# - RFP Outline: {full_outline}  
# - Section Outline: {current_section_outline}  
# - RFP Summary: {procurement_summary}  
# - Knowledge Base: {past_performance}
# """
#     return prompt

  #================================================prompt v6 with generic part3=======================================================
# def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
# Mode: You must act as both **validator and generator**.

# You are drafting ONE specific RFP response section using the inputs provided.  
# Before generating content, **analyze** each component in the `section_outline` based on the available inputs.

# ---

# ## Phase 1: Agentic Validation

# For each required section component:

# 1. Check the `Knowledge Base`:
#    - If **clear, detailed, and relevant content exists**, mark as usable.
#    - If only **partial** or **lightly relevant content** exists, use it carefully, indicating its limitations.
#    - If **no content is available**, skip it unless it qualifies for generalization (see Rule 2).

#    **Also check the `rfp_summary`:**
#    - If relevant content exists here, treat it as valid input.
#    - Do **not** treat such content as generic or LLM-generated.

#    **Use the `section_outline` for structure, but if it includes informative or implied content,** you may paraphrase it.  
#    Do not apply the generic label if using phrasing or intent from `section_outline`.

# 2. **If a section is NOT covered in any inputs (knowledge base, summary, outline), but is universally expected and not RFP-specific:**
#    - You may generate **generic, industry-standard content** only if:
#      - It does NOT involve past performance, corporate experience, or project-specific details.
#      - It reflects widely accepted best practices (e.g., project management standards, QA processes).
#    - Clearly label such content using this exact placeholder:  
#      `[This section is based on factual information and general practices.]`

# 3. Do **NOT** fabricate or generalize content in the following areas:
#    - Past performance
#    - Company-specific capabilities
#    - Project history
#    - Anything that references solicitation-specific numbers, thresholds, systems, or terminology  
#    If such data is missing, explicitly state:  
#      `"Content not available in the provided knowledge base."`

# 4. Track:
#    - Items with complete matches
#    - Items only referenced in the summary
#    - Items with no data and not eligible for generalization

# ---

# ## Phase 2: Final Response Generation

# Generate the full section **only after completing validation**.

# **Rules for Response:**
# - Follow the `section_outline` structure and order.
# - Use only content validated in Phase 1.
# - Maintain a persuasive, professional tone ("we" or "TENANT_NAME").
# - Do not add company-specific details unless explicitly present in the inputs.
# - Cite all sources using `// Source: [Exact content snippet]`.
# - If skipping a section due to no valid content, state:  
#   `"Content not available in the provided knowledge base."`
# - If inserting LLM-generated generic content (not derived from any input), clearly label it:  
#   `[This section is based on factual information and general practices.]`
# - **Important**:  
#   Do **not** apply this label to content that is paraphrased, inferred, or directly taken from:
#   - Any part of the `knowledge_base`
#   - Any relevant information in the `rfp_summary`
#   - Informative phrasing or intent embedded in `section_outline`

# ---

# ## Output Format:

# **Section Title:** {current_section_outline}  
# **TOC Reference:** Appears in TOC: Yes/No  

# **Generated Content:**  
# [Structured, validation-based content with clear sourcing]

# ---

# **HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
# - Direct Matches from Knowledge Base: [List]
# - Only Mentioned in Summary, Not Detailed: [List]
# - Generic Content Substituted For: [List]
# - Skipped Due to Missing Data: [List]
# - Duplication Check: Pass/Fail  
# - Sequence Check: Pass/Fail

# ---

# Inputs:
# - RFP Outline: {full_outline}  
# - Section Outline: {current_section_outline}  
# - RFP Summary: {procurement_summary}  
# - Knowledge Base: {past_performance}
# """
#     return prompt

#================================================prompt v6 with generic part2=======================================================
# def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
# Mode: You must act as both **validator and generator**.

# You are drafting ONE specific RFP response section using the inputs provided.  
# Before generating content, **analyze** each component in the `section_outline` based on the available inputs.

# ---

# ## Phase 1: Agentic Validation

# For each required section component:

# 1. Check the `Knowledge Base`:
#    - If **clear, detailed, and relevant content exists**, mark as usable.
#    - If only **partial** or **lightly relevant content** exists, use it carefully, indicating its limitations.
#    - If **no content is available**, skip it unless it qualifies for generalization (see Rule 2).

# 2. **If a section is NOT covered in the Knowledge Base**, but is universally expected and not RFP-specific:
#    - You may generate **generic, industry-standard content** only if:
#      - It does NOT involve past performance, corporate experience, or project-specific details.
#      - It reflects widely accepted best practices (e.g., project management standards, QA processes).
#    - Clearly label such content using this exact placeholder:  
#      `[This section is based on factual information and general practices.]`

# 3. Do **NOT** fabricate or generalize content in the following areas:
#    - Past performance
#    - Company-specific capabilities
#    - Project history
#    - Anything that references solicitation-specific numbers, thresholds, systems, or terminology  
#    If such data is missing, explicitly state:  
#      `"Content not available in the provided knowledge base."`

# 4. Track:
#    - Items with complete matches
#    - Items only referenced in the summary
#    - Items with no data and not eligible for generalization

# ---

# ## Phase 2: Final Response Generation

# Generate the full section **only after completing validation**.

# **Rules for Response:**
# - Follow the `section_outline` structure and order.
# - Use only content validated in Phase 1.
# - Maintain a persuasive, professional tone ("we" or "TENANT_NAME").
# - Do not add company-specific details unless explicitly present in the inputs.
# - Cite all sources using `// Source: [Exact content snippet]`.
# - If skipping a section due to no valid content, state:  
#   `"Content not available in the provided knowledge base."`
# - If inserting LLM-generated generic content (not derived from any input), clearly label it:  
#   `[This section is based on factual information and general practices.]`
# - **Important**:  
#   Do **not** apply this label to sections that are based on:
#   - Any part of the `knowledge_base`
#   - Any references in the `rfp_summary`
#   - Headings, structure, or wording from `section_outline`  
#   Use this label **only** for full paragraph content generated independently by the model.

# ---

# ## Output Format:

# **Section Title:** {current_section_outline}  
# **TOC Reference:** Appears in TOC: Yes/No  

# **Generated Content:**  
# [Structured, validation-based content with clear sourcing]

# ---

# **HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
# - Direct Matches from Knowledge Base: [List]
# - Only Mentioned in Summary, Not Detailed: [List]
# - Generic Content Substituted For: [List]
# - Skipped Due to Missing Data: [List]
# - Duplication Check: Pass/Fail  
# - Sequence Check: Pass/Fail

# ---

# Inputs:
# - RFP Outline: {full_outline}  
# - Section Outline: {current_section_outline}  
# - RFP Summary: {procurement_summary}  
# - Knowledge Base: {past_performance}
# """
#     return prompt

#================================================prompt v6 with generic part1=======================================================
# def get_response_v6(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
# Mode: You must act as both **validator and generator**.

# You are drafting ONE specific RFP response section using the inputs provided.  
# Before generating content, **analyze** each component in the `section_outline` based on the available inputs.

# ---

# ## Phase 1: Agentic Validation

# For each required section component:

# 1. Check the `Knowledge Base`:
#    - If **clear, detailed, and relevant content exists**, mark as usable.
#    - If only **partial** or **lightly relevant content** exists, use it carefully, indicating its limitations.
#    - If **no content is available**, skip it unless it qualifies for generalization (see Rule 2).

# 2. **If a section is NOT covered in Knowledge Base**, but is universally expected and not RFP-specific:
#    - Generate **generic industry-standard content** only if:
#      - It does NOT involve past performance, corporate experience, or project-specific details.
#      - It reflects widely accepted best practices (e.g., project management standards, QA processes).
#    - Clearly mark it as **generic** using:  
#      `"The following reflects standard best practices in the absence of RFP-specific data."`

# 3. Do **NOT** fabricate content under any of the following:
#    - Past performance
#    - Company-specific capabilities
#    - Project history
#    - Anything that references solicitation-specific numbers, thresholds, systems, etc.
#    - If such data is missing, state:  
#      `"Content not available in the provided knowledge base or summary."`

# 4. Track:
#    - Items with complete matches
#    - Items only referenced in the summary
#    - Items with no data and not eligible for generalization

# ---

# ## Phase 2: Final Response Generation

# Generate the full section **only after completing validation**.

# **Rules for Response:**
# - Follow the `section_outline` structure and order.
# - Use only content approved in Phase 1.
# - Maintain a persuasive, professional tone ("we" or "TENANT_NAME").
# - Do not add company-specific details unless explicitly present in the inputs.
# - Cite all sources using `// Source: [Exact content snippet]`.
# - If skipping a section due to no valid content, state:  
#   `"Content not available in the provided knowledge base or summary."`
# - If inserting generic content, label it as:  
#   `"This section is based on standard industry practices."`

# ---

# ## Output Format:

# **Section Title:** {current_section_outline}  
# **TOC Reference:** Appears in TOC: Yes/No  

# **Generated Content:**  
# [Structured, validation-based content with clear sourcing]

# ---

# **HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
# - Direct Matches from Knowledge Base: [List]
# - Only Mentioned in Summary, Not Detailed: [List]
# - Generic Content Substituted For: [List]
# - Skipped Due to Missing Data: [List]
# - Duplication Check: Pass/Fail  
# - Sequence Check: Pass/Fail

# ---

# Inputs:
# - RFP Outline: {full_outline}  
# - Section Outline: {current_section_outline}  
# - RFP Summary: {procurement_summary}  
# - Knowledge Base: {past_performance}
# """
#     return prompt

 

  #================================================old breakdown==============================
# def get_outline_breakdown_prompt(outline):
#     prompt = f"""Role: Intelligent Outline Breakdown Specialist

# Objective:
# - Transform the provided outline into a structured JSON representation with complete content
# - Optimize section breakdown for proposal writing workflow
# - Ensure each section is self-sufficient and independent
# - Include the exact content from the outline under each section

# Breakdown Principles:
# 1. Section Consolidation Strategy:
#    - Identify and preserve any content before the Table of Contents (TOC) as a separate section
#    - Include the Table of Contents (TOC) itself as a distinct section
#    - Use the TOC as the primary guide for identifying major sections and their subsections
#    - Combine small, related sections (3-4 consecutive sections with minimal subsections)
#    - Break down large, complex sections with multiple substantive subsections
#    - Maintain original outline structure and hierarchy
#    - Create sections that can be written independently

# 2. JSON Output Requirements:
#    - Capture exact section and subsection titles
#    - Include the complete original content under each subsection
#    - Number sections sequentially
#    - Group related small sections
#    - Ensure each section is comprehensive yet manageable
#    - Include "Pre-TOC Content" section if content exists before the TOC
#    - Include "Table of Contents" section with the original TOC content

# 3. Content Preservation Guidelines:
#    - Copy the exact content from the outline verbatim under each subsection
#    - Include all instructions, requirements, deliverables, and specifications
#    - Preserve formatting, bullet points, and detailed descriptions
#    - Do not summarize or paraphrase - copy the complete original text
#    - Maintain all technical details and specific requirements

# 4. Processing Guidelines:
#   - Identify and separate content that appears before the Table of Contents
#    - Preserve the Table of Contents as its own section
#    - Ignore administrative elements (page numbers, headers, footers)
#    - Focus on substantive content sections
#    - Preserve original section order and relationships
#    - Create logical, coherent section groups

# 5. Writing Workflow Considerations:
#    - Enable proposal writers to tackle sections in multiple writing sessions
#    - Make each section a self-contained writing unit with complete content
#    - Allow flexibility in writing sequence while maintaining document flow

# Specific Handling Rules:
# - Always check for and preserve content that appears before the Table of Contents
# - Always include the Table of Contents itself as a separate section in the JSON output
# - If a section has multiple complex subsections, break it into separate entries
# - For sections with minimal content, combine them into logical groups
# - Maintain the semantic integrity of the original outline
# - Include ALL content that appears under each section/subsection in the original outline

# Input Outline:
# {outline}


# Example JSON: REMEMBER THAT IT IS COMPULSORY TO FOLLOW THIS JSON LAYOUT ALWAYS.

# Example JSON: REMEMBER THAT IT IS COMPULSORY TO FOLLOW THIS JSON LAYOUT ALWAYS.

# {{
#   "sections": [
#     {{
#       "section_number": "pre-toc",
#       "section_title": "Content Before Table of Contents",
#       "subsections": [
#         {{
#           "subsection_title": "Cover Page",
#           "content": "Complete original content that appears before the TOC including all text exactly as written in the source outline."
#         }}
#       ]
#     }},
#     {{
#       "section_number": "toc",
#       "section_title": "Table of Contents",
#       "subsections": [
#         {{
#           "subsection_title": "Table of Contents",
#           "content": "Complete original TOC content exactly as written in the source outline."
#         }}
#       ]
#     }},
#     {{
#       "section_number": "1-7",
#       "section_title": "Sections 1-7: Administrative and Compliance Responses",
#       "subsections": [
#         {{
#           "subsection_title": "1 Assumptions",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "  1.1 Technical Assumptions",
#           "content": "Complete original content from the outline for this subsection including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "  1.2 Price Assumptions",
#           "content": "Complete original content from the outline for this subsection including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "2 SF 1449",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "3 Acknowledgment of Solicitation Amendments",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "4 Completion of Representations",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "5 Attestation",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "6 Conflict of Interest Mitigation Plan",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }},
#         {{
#           "subsection_title": "7 GSA Federal Supply Schedule (FSS)",
#           "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
#         }}
#       ]
#     }}
#   ]
# }}

# Additional Guidance:
# - Prioritize clarity and writability
# - Ensure comprehensive coverage
# - Maintain the document's original intent and structure
# - If there is no subsection available for a section, then include the section_title as a subsection_title
# - CRITICAL: Include the complete, exact content from the original outline under each subsection's "content" field
# - Do not summarize, paraphrase, or modify the original content - copy it verbatim
# - Include all technical specifications, requirements, deliverables, and instructions exactly as they appear
# - Do not add additional text/commentary at the beginning or end of the response e.g., Here is the response ... or Okay, I will now generate the content or other unwanted/unrelevant commentary.
# """
#     return prompt

#=========================================prompt v3=================================================================
# def get_response_v3(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Expert Federal and Non-Federal Proposal Writer | Response Compliance Strategist at COGVEEL

# You are tasked with drafting a final response for **one specific section** of a proposal. The inputs provided are:

# - `rfp_outline`: The full Table of Contents and outline of the RFP. Use this only for reference and structure.
# - `section_outline`: A detailed breakdown of the specific section for which you must generate the response. This defines the structure and expectations of your output.
# - `rfp_summary`: A high-level summary of the RFP. Only use this if it is **directly relevant** to the section being generated.
# - `knowledge_base`: The main content source from which your response should be constructed.

# ---

# ## Generation Rules:

# 1. **STRICTLY FOLLOW** the `section_outline` structure while generating your response.
# 2. If applicable, refer to the `rfp_outline` to understand positioning of the section within the RFP — e.g., whether it appears in TOC or not.
# 3. **Use `rfp_summary` only if it’s relevant** to the section being generated (e.g., skip it entirely for "Past Performance" or other irrelevant sections).
# 4. **Your primary content source is `knowledge_base`.** If no content is found for a required element, clearly state:  
#    `"Content not available in the provided knowledge base."`
# 5. The response should be written in formal, persuasive, and professional tone using **"we" or "COGVEEL"** voice.
# 6. Ensure clarity, relevance, and compliance with the outline structure.
# 7. Do not hallucinate or fabricate information beyond the knowledge base.

# ---

# ## Output Format:
# **Section Title:** {current_section_outline}

# **TOC Reference:** Confirm whether this section appears in the RFP Outline TOC: Yes/No

# **Compliance Note:** Fully aligned with the given section_outline structure.

# **Generated Content:**
# [Insert the fully written section content here, broken down according to the section_outline]

# ---
# Inputs:
# - RFP Outline: {full_outline}
# - Section Outline: {current_section_outline}
# - RFP Summary: {procurement_summary}
# - Knowledge Base: {past_performance}
# """
#     return prompt
#=======================================prompt v1=====================================================
# def get_response_v1(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Expert Proposal Writer for Federal and non federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked with generating a final-draft response for one specific section of a federal or Non federal RFP response. You must **adhere strictly** to the provided `current_section_outline`, use real content from `past_performance`, and ensure everything aligns with the `procurement_summary`.

# ---

# ## Critical Output Rules
# **You must follow ALL these rules. Violating any rule = failed response.**

# 1. **Output must include every heading and subheading from the section outline.**
#    - Use Markdown (###) for each `subsection_title`.
#    - Never skip, rename, rephrase, reorder, or remove any heading.
#    - Each heading should appear **only once**.
#    - Never repeat headings later in the content.
#    - Place content **under** the appropriate heading, not inside or above it.

# 2.**Do not copy or slightly reword outline content.**
#    - Treat outline text as **instructional only**, not final content.
#    - Rephrase and expand it to include detailed explanations, metrics, examples, and clear value-adds.

# 3.**Absolutely NO hallucinations.**
#    - Do not invent tools, teams, processes, outcomes, or stats.
#    - You may only use concepts that are directly inferable or supported from:
#      - `current_section_outline`
#      - `past_performance`
#      - `procurement_summary`
#    - If information is missing, use placeholders like:  
#      `[Insert Tool Name]`, `[Insert KPI]`, `[Insert Workflow Step]`.

# 4.**No duplicated or repeated sentences.**
#    - Avoid repeating the same benefit or technical point across subsections.
#    - Use varied language and avoid echoing phrases like "we ensure quality..." across bullets.

# 5. **Use Reflective Mode After Each Section:**
#    - After completing each subsection, pause and verify:
#      - "Did I expand this content meaningfully?"
#      - "Did I reuse or copy any outline sentence?"
#      - "Is this consistent with the procurement goal?"
#      - "Did I reference relevant past performance?"
#      - "Is this too vague or repetitive?"

# ---

# ## Structure-Driven Generation

# Start with the `current_section_outline` (parsed below) and process each subsection one by one.

# ### If Table of Contents (`toc`) is present:
# - Reproduce it **verbatim**, exactly as given in the `toc` section.
# - Do not summarize, interpret, or paraphrase it.

# ### For all `section_title` and `subsection_title` entries:
# - Use proper Markdown heading formatting.
# - Maintain the order and nesting of sections as given.
# - Write original, detailed content under each heading using guidance in the `content` field.

# ---

# ## Evidence-Based Writing Instructions

# For every subsection:

# - **Translate the outline description** into specific methods, steps, and tools.
# - **Back all claims with examples or metrics** from `past_performance`.
# - **Highlight differentiators** that align with the client's mission, RFP goals, and quality standards.
# - **Map your solution** to procurement objectives such as quality, timeliness, scalability, or compliance.
# - **Use industry terms and frameworks** that evaluators will recognize, only if they’re supported.

# ---

# ## Content Sources

# - `current_section_outline`: Fully parsed content and requirements for this section.
# - `past_performance`: Full text of prior performance and achievements.
# - `procurement_summary`: Details of client goals, success criteria, pain points, etc.

# ---

# ## Output Formatting

# - Use **Markdown** for:
#   - Section/subsection titles (`###`)
#   - Bullet points (with `-`)
#   - Tables (in valid Markdown table format, preserved from outline)
# - Ensure all numerical, pricing, or structured data matches outline values **exactly**.
# - Do not approximate or alter figures from the outline.

# ---

# ## Final Output Expectations

# - **No summary or commentary. Only deliver the final content.**
# - **Do not add new headings** unless you are filling a placeholder marked in the outline.
# - **Do not omit or merge any subsections**.
# - **Each `subsection_title` must appear once and only once** as a heading.
# - Treat this like a **ready-to-submit response**—no rough drafts or filler.

# ---

# ## INPUT DATA
# ### Complete Outline
# {full_outline}
# ### Current Section Outline with Complete Content:
# {current_section_outline}

# ### Past Performance:
# {past_performance}

# ### Procurement Summary:
# {procurement_summary}

# BEGIN RESPONSE GENERATION NOW for  Current Section Outline with Complete Content:
# """
#     return prompt
  
#=======================================no summary=========================================================== 
# def get_response_v1_no_summary(full_outline, current_section_outline, past_performance):
#     prompt = f"""

# Role: Expert Proposal Writer for Federal and Non-Federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked with generating a final-draft response for one specific section of an RFP. You must use only the content from the `past_performance` to craft the response. The `full_outline` and `current_section_outline` provide structure and guidance only—**do not copy text from them directly**.

# ---

# ## CRITICAL OUTPUT RULES  
# **You must follow all instructions precisely. Any violations result in a failed response.**

# 1. **Strict Section Structure**  
#    - Use every heading and subheading from the `current_section_outline`.  
#    - Use proper Markdown formatting:  
#      - `###` for `subsection_title`  
#    - Do not reorder, skip, rename, or add headings unless a placeholder exists.  
#    - Each heading should appear **once only** and must be followed by its corresponding response.  

# 2. **Content Source Restrictions**  
#    - **You may only use material from `past_performance`.**  
#    - Do not fabricate processes, teams, metrics, or tools.  
#    - If specific information is missing, insert placeholders like `[Insert Tool Name]` or `[Insert KPI]`.  

# 3. **Outline Is Not Content**  
#    - Treat all outline content (from `current_section_outline` and `full_outline`) as **guidance only**.  
#    - Do **not** reuse or slightly reword outline content—reframe it with real examples from past performance.  

# 4. **Unique and Non-Repetitive Content**  
#    - Avoid repeated phrasing or duplicated benefits across subsections.  
#    - Each point should offer new, specific insights based on past performance.  

# 5. **After Each Subsection: Self-Check**  
#    - Ask:
#      - Did I expand this using real examples from past performance?
#      - Did I avoid copying outline content?
#      - Is it specific, relevant, and not generic?

# ---

# ## OUTPUT STRUCTURE

# Start by reproducing any provided Table of Contents (`toc`) **exactly**, if present in `current_section_outline`.

# Then generate the final response section-by-section:

# - Use `section_title` and `subsection_title` structure from `current_section_outline`.
# - Under each heading, write detailed, value-based content drawn exclusively from `past_performance`.
# - Use examples, achievements, tools, timelines, and metrics as proof.

# ---

# ## FORMATTING RULES

# - Use Markdown syntax:
#   - `###` for subsections
#   - `-` for bullet points
#   - Valid Markdown tables if applicable
# - Do not include summaries, introductions, or commentary—only the response content itself.

# ---

# ## INPUTS

# ### Complete Outline:
# {full_outline}

# ### Current Section Outline with Complete Content:
# {current_section_outline}

# ### Past Performance:
# {past_performance}

# ---

# BEGIN RESPONSE GENERATION NOW for the section defined in `Current Section Outline with Complete Content` using only the `Past Performance`.
# """
#     return prompt
#===========================================my no summary==========================================================
 
# def get_response_v1_no_summary(full_outline, current_section_outline, past_performance):
#     prompt = f"""
# Role: Expert Proposal Writer for Federal and non federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked with generating a final-draft response for one specific section of a federal or Non federal RFP response. You must **adhere strictly** to the provided `current_section_outline`, use real content from `past_performance`.

# ---

# ## Critical Output Rules
# **You must follow ALL these rules. Violating any rule = failed response.**

# 1. **Output must include every heading and subheading from the section outline.**
#    - Use Markdown (###) for each `subsection_title`.
#    - Never skip, rename, rephrase, reorder, or remove any heading.
#    - Each heading should appear **only once**.
#    - Never repeat headings later in the content.
#    - Place content **under** the appropriate heading, not inside or above it.

# 2.**Do not copy or slightly reword outline content.**
#    - Treat outline text as **instructional only**, not final content.
#    - Rephrase and expand it to include detailed explanations, metrics, examples, and clear value-adds.

# 3.**Absolutely NO hallucinations.**
#    - Do not invent tools, teams, processes, outcomes, or stats.
#    - You may only use concepts that are directly inferable or supported from:
#      - `current_section_outline`
#      - `past_performance`
#    - If information is missing, use placeholders like:  
#      `[Insert Tool Name]`, `[Insert KPI]`, `[Insert Workflow Step]`.

# 4.**No duplicated or repeated sentences.**
#    - Avoid repeating the same benefit or technical point across subsections.
#    - Use varied language and avoid echoing phrases like "we ensure quality..." across bullets.

# 5. **Use Reflective Mode After Each Section:**
#    - After completing each subsection, pause and verify:
#      - "Did I expand this content meaningfully?"
#      - "Did I reuse or copy any outline sentence?"
#      - "Is this consistent with the procurement goal?"
#      - "Did I reference relevant past performance?"
#      - "Is this too vague or repetitive?"

# ---

# ## Structure-Driven Generation

# Start with the `current_section_outline` (parsed below) and process each subsection one by one.

# ### If Table of Contents (`toc`) is present:
# - Reproduce it **verbatim**, exactly as given in the `toc` section.
# - Do not summarize, interpret, or paraphrase it.

# ### For all `section_title` and `subsection_title` entries:
# - Use proper Markdown heading formatting.
# - Maintain the order and nesting of sections as given.
# - Write original, detailed content under each heading using guidance in the `content` field.

# ---

# ## Evidence-Based Writing Instructions

# For every subsection:

# - **Translate the outline description** into specific methods, steps, and tools.
# - **Back all claims with examples or metrics** from `past_performance`.
# - **Highlight differentiators** that align with the client's mission, RFP goals, and quality standards.
# - **Map your solution** to procurement objectives such as quality, timeliness, scalability, or compliance.
# - **Use industry terms and frameworks** that evaluators will recognize, only if they’re supported.

# ---

# ## Content Sources

# - `current_section_outline`: Fully parsed content and requirements for this section.
# - `past_performance`: Full text of prior performance and achievements.

# ---

# ## Output Formatting

# - Use **Markdown** for:
#   - Section/subsection titles (`###`)
#   - Bullet points (with `-`)
#   - Tables (in valid Markdown table format, preserved from outline)
# - Ensure all numerical, pricing, or structured data matches outline values **exactly**.
# - Do not approximate or alter figures from the outline.

# ---

# ## Final Output Expectations

# - **No summary or commentary. Only deliver the final content.**
# - **Do not add new headings** unless you are filling a placeholder marked in the outline.
# - **Do not omit or merge any subsections**.
# - **Each `subsection_title` must appear once and only once** as a heading.
# - Treat this like a **ready-to-submit response**—no rough drafts or filler.

# ---

# ## INPUT DATA
# ### Complete Outline
# {full_outline}
# ### Current Section Outline with Complete Content:
# {current_section_outline}

# ### Past Performance:
# {past_performance}

# BEGIN RESPONSE GENERATION NOW for  Current Section Outline with Complete Content:
# """
#     return prompt

#=========================prompt v2=========================================================  
# def get_response_v1(outline_content, subsection_titles, past_performance_content, summary_content):
#     prompt = f"""
# Role: Expert Proposal Writer for Federal and non federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked from Company "COGVEEL" with generating a final-draft response for one specific section of a federal or Non federal RFP response. You must **adhere strictly** to the provided `current_section_outline`, use real content from `past_performance`, and ensure everything aligns with the `procurement_summary`.

# ---

# ## Critical Output Rules
# **You must follow ALL these rules. Violating any rule = failed response.**

# 1. **Output must include every heading and subheading from the section outline.**
#    - Use Markdown (###) for each `subsection_title`.
#    - Never skip, rename, rephrase, reorder, or remove any heading.
#    - Each heading should appear **only once**.
#    - Never repeat headings later in the content.
#    - Place content **under** the appropriate heading, not inside or above it.

# 2.**Do not copy or slightly reword outline content.**
#    - Treat outline text as **instructional only**, not final content.
#    - Rephrase and expand it to include detailed explanations, metrics, examples, and clear value-adds.

# 3.**Absolutely NO hallucinations.**
#    - Do not invent tools, teams, processes, outcomes, or stats.
#    - You may only use concepts that are directly inferable or supported from:
#      - `current_section_outline`
#      - `past_performance`
#      - `procurement_summary`
#    - If information is missing, use placeholders like:  
#      `[Insert Tool Name]`, `[Insert KPI]`, `[Insert Workflow Step]`.

# 4.**No duplicated or repeated sentences.**
#    - Avoid repeating the same benefit or technical point across subsections.
#    - Use varied language and avoid echoing phrases like "we ensure quality..." across bullets.

# 5. **Use Reflective Mode After Each Section:**
#    - After completing each subsection, pause and verify:
#      - "Did I expand this content meaningfully?"
#      - "Did I reuse or copy any outline sentence?"
#      - "Is this consistent with the procurement goal?"
#      - "Did I reference relevant past performance?"
#      - "Is this too vague or repetitive?"

# ---

# ## Structure-Driven Generation

# Start with the `current_section_outline` (parsed below) and process each subsection one by one.

# ### If Table of Contents (`toc`) is present:
# - Reproduce it **verbatim**, exactly as given in the `toc` section.
# - Do not summarize, interpret, or paraphrase it.

# ### For all `section_title` and `subsection_title` entries:
# - Use proper Markdown heading formatting.
# - Maintain the order and nesting of sections as given.
# - Write original, detailed content under each heading using guidance in the `content` field.

# ---

# ## Evidence-Based Writing Instructions

# For every subsection:

# - **Translate the outline description** into specific methods, steps, and tools.
# - **Back all claims with examples or metrics** from `past_performance`.
# - **Highlight differentiators** that align with the client's mission, RFP goals, and quality standards.
# - **Map your solution** to procurement objectives such as quality, timeliness, scalability, or compliance.
# - **Use industry terms and frameworks** that evaluators will recognize, only if they’re supported.

# ---

# ## Content Sources

# - `current_section_outline`: Fully parsed content and requirements for this section.
# - `past_performance`: Full text of prior performance and achievements.
# - `procurement_summary`: Details of client goals, success criteria, pain points, etc.

# ---

# ## Output Formatting

# - Use **Markdown** for:
#   - Section/subsection titles (`###`)
#   - Bullet points (with `-`)
#   - Tables (in valid Markdown table format, preserved from outline)
# - Ensure all numerical, pricing, or structured data matches outline values **exactly**.
# - Do not approximate or alter figures from the outline.

# ---

# ## Final Output Expectations

# - **No summary or commentary. Only deliver the final content.**
# - **Do not add new headings** unless you are filling a placeholder marked in the outline.
# - **Do not omit or merge any subsections**.
# - **Each `subsection_title` must appear once and only once** as a heading.
# - Treat this like a **ready-to-submit response**—no rough drafts or filler.

# ---

# ## INPUT DATA
# ### Complete Outline
# {outline_content}
# ### Current Section Outline with Complete Content:
# {subsection_titles}

# ### Past Performance:
# {past_performance_content}

# ### Procurement Summary:
# {summary_content}

# BEGIN RESPONSE GENERATION NOW for  Current Section Outline with Complete Content:
# """
#     return prompt
  #=================================prompt old================================================
# def get_response_v1(outline_content, subsection_titles, past_performance_content, summary_content):
#     prompt = f"""
# Role: Expert Proposal Writer for Federal and non federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked from Company "COGVEEL" with generating a final-draft response for one specific section of a federal or Non federal RFP response. You must **adhere strictly** to the provided `current_section_outline`, use real content from `past_performance`, and ensure everything aligns with the `procurement_summary`.

# ---

# ## Critical Output Rules
# **You must follow ALL these rules. Violating any rule = failed response.**

# 1. **Output must include every heading and subheading from the section outline.**
#    - Use Markdown (###) for each `subsection_title`.
#    - Never skip, rename, rephrase, reorder, or remove any heading.
#    - Each heading should appear **only once**.
#    - Never repeat headings later in the content.
#    - Place content **under** the appropriate heading, not inside or above it.

# 2. **Do not copy or slightly reword outline content.**
#    - Treat outline and subsection content as **instructional only**, never as content to reuse.
#    - **You are strictly prohibited from copying, echoing, paraphrasing, or summarizing any text from `current_section_outline` or `subsection_titles`.**
#    - Rephrase and expand based **only** on what's available in `past_performance` or `procurement_summary`.

# 3. **Absolutely NO hallucinations.**
#    - Do not invent tools, teams, processes, outcomes, or stats.
#    - You may only use concepts that are directly inferable or supported from:
#      - `current_section_outline` (for structure/guidance only)
#      - `past_performance`
#      - `procurement_summary`
#    - If information is missing, use placeholders like:  
#      `[Insert Tool Name]`, `[Insert KPI]`, `[Insert Workflow Step]`.

# 4. **No duplicated or repeated sentences.**
#    - Avoid repeating the same benefit or technical point across subsections.
#    - Use varied language and avoid echoing phrases like "we ensure quality..." across bullets.

# 5. **Use Reflective Mode After Each Section:**
#    - After completing each subsection, pause and verify:
#      - "Did I expand this content meaningfully?"
#      - "Did I reuse or copy any outline sentence?"
#      - "Is this consistent with the procurement goal?"
#      - "Did I reference relevant past performance?"
#      - "Is this too vague or repetitive?"

# ---

# ## Structure-Driven Generation

# Start with the `current_section_outline` (parsed below) and process each subsection one by one.

# ### If Table of Contents (`toc`) is present:
# - Reproduce it **verbatim**, exactly as given in the `toc` section.
# - Do not summarize, interpret, or paraphrase it.

# ### For all `section_title` and `subsection_title` entries:
# - Use proper Markdown heading formatting.
# - Maintain the order and nesting of sections as given.
# - Write original, detailed content under each heading using guidance in the `content` field.
# - **Only use source information from `past_performance` or `procurement_summary`.**

# ---

# ## Evidence-Based Writing Instructions

# For every subsection:

# - **Translate the outline description** into specific methods, steps, and tools.
# - **Back all claims with examples or metrics** from `past_performance`.
# - **Highlight differentiators** that align with the client's mission, RFP goals, and quality standards.
# - **Map your solution** to procurement objectives such as quality, timeliness, scalability, or compliance.
# - **Use industry terms and frameworks** that evaluators will recognize, only if they’re supported.

# > **If no relevant information is found in `past_performance` or `procurement_summary`, clearly state:**
# > **“[No available data for this section based on provided past performance or procurement summary.]”**

# ---

# ## Content Sources

# - `current_section_outline`: Fully parsed content and requirements for this section. Used only for guidance and structure.
# - `past_performance`: Full text of prior performance and achievements.
# - `procurement_summary`: Details of client goals, success criteria, pain points, etc.

# ---

# ## Output Formatting

# - Use **Markdown** for:
#   - Section/subsection titles (`###`)
#   - Bullet points (with `-`)
#   - Tables (in valid Markdown table format, preserved from outline)
# - Ensure all numerical, pricing, or structured data matches outline values **exactly**.
# - Do not approximate or alter figures from the outline.

# ---

# ## Final Output Expectations

# - **No summary or commentary. Only deliver the final content.**
# - **Do not add new headings** unless you are filling a placeholder marked in the outline.
# - **Do not omit or merge any subsections**.
# - **Each `subsection_title` must appear once and only once** as a heading.
# - Treat this like a **ready-to-submit response**—no rough drafts or filler.

# ---

# ## INPUT DATA
# ### Complete Outline
# {outline_content}
# ### Current Section Outline with Complete Content:
# {subsection_titles}

# ### Past Performance:
# {past_performance_content}

# ### Procurement Summary:
# {summary_content}

# BEGIN RESPONSE GENERATION NOW for Current Section Outline with Complete Content:
# """
#     return prompt
  #=============================promptv2========================================================
  
# def get_response_v1(outline_content, subsection_titles, past_performance_content, summary_content):
#     prompt = f"""
# Role: Expert Proposal Writer for Federal and non federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked from Company "COGVEEL" with generating a final-draft response for one specific section of a federal or Non federal RFP response. You must **adhere strictly** to the provided `current_section_outline`, use real content from `past_performance`, and ensure everything aligns with the `procurement_summary`.

# ---

# ## Critical Output Rules
# **You must follow ALL these rules. Violating any rule = failed response.**

# 1. **Output must include every heading and subheading from the section outline.**
#    - Use Markdown (###) for each `subsection_title`.
#    - Never skip, rename, rephrase, reorder, or remove any heading.
#    - Each heading should appear **only once**.
#    - Never repeat headings later in the content.
#    - Place content **under** the appropriate heading, not inside or above it.

# 2.**Do not copy or slightly reword outline content.**
#    - Treat outline text as **instructional only**, not final content.
#    - Rephrase and expand it to include detailed explanations, metrics, examples, and clear value-adds.

# 3.**Absolutely NO hallucinations.**
#    - Do not invent tools, teams, processes, outcomes, or stats.
#    - You may only use concepts that are directly inferable or supported from:
#      - `current_section_outline`
#      - `past_performance`
#      - `procurement_summary`
#    - If information is missing, use placeholders like:  
#      `[Insert Tool Name]`, `[Insert KPI]`, `[Insert Workflow Step]`.

# 4.**No duplicated or repeated sentences.**
#    - Avoid repeating the same benefit or technical point across subsections.
#    - Use varied language and avoid echoing phrases like "we ensure quality..." across bullets.

# 5. **Use Reflective Mode After Each Section:**
#    - After completing each subsection, pause and verify:
#      - "Did I expand this content meaningfully?"
#      - "Did I reuse or copy any outline sentence?"
#      - "Is this consistent with the procurement goal?"
#      - "Did I reference relevant past performance?"
#      - "Is this too vague or repetitive?"

# ---

# ## Structure-Driven Generation

# Start with the `current_section_outline` (parsed below) and process each subsection one by one.

# ### If Table of Contents (`toc`) is present:
# - Reproduce it **verbatim**, exactly as given in the `toc` section.
# - Do not summarize, interpret, or paraphrase it.

# ### For all `section_title` and `subsection_title` entries:
# - Use proper Markdown heading formatting.
# - Maintain the order and nesting of sections as given.
# - Write original, detailed content under each heading using guidance in the `content` field.

# ---

# ## Evidence-Based Writing Instructions

# For every subsection:

# - **Translate the outline description** into specific methods, steps, and tools.
# - **Back all claims with examples or metrics** from `past_performance`.
# - **Highlight differentiators** that align with the client's mission, RFP goals, and quality standards.
# - **Map your solution** to procurement objectives such as quality, timeliness, scalability, or compliance.
# - **Use industry terms and frameworks** that evaluators will recognize, only if they’re supported.

# ---

# ## Content Sources

# - `current_section_outline`: Fully parsed content and requirements for this section.
# - `past_performance`: Full text of prior performance and achievements.
# - `procurement_summary`: Details of client goals, success criteria, pain points, etc.

# ---

# ## Output Formatting

# - Use **Markdown** for:
#   - Section/subsection titles (`###`)
#   - Bullet points (with `-`)
#   - Tables (in valid Markdown table format, preserved from outline)
# - Ensure all numerical, pricing, or structured data matches outline values **exactly**.
# - Do not approximate or alter figures from the outline.

# ---

# ## Final Output Expectations

# - **No summary or commentary. Only deliver the final content.**
# - **Do not add new headings** unless you are filling a placeholder marked in the outline.
# - **Do not omit or merge any subsections**.
# - **Each `subsection_title` must appear once and only once** as a heading.
# - Treat this like a **ready-to-submit response**—no rough drafts or filler.

# ---

# ## INPUT DATA
# ### Complete Outline
# {outline_content}
# ### Current Section Outline with Complete Content:
# {subsection_titles}

# ### Past Performance:
# {past_performance_content}

# ### Procurement Summary:
# {summary_content}

# BEGIN RESPONSE GENERATION NOW for  Current Section Outline with Complete Content:
# """
#     return prompt
  #===========================prompt v01====================================================
# def get_response_generation_prompt(full_outline, current_section_outline, past_performance, procurement_summary):
#     prompt = f"""
# Role: Expert Proposal Writer for Federal and non federal Procurement | Response Compliance Strategist

# You are a highly specialized proposal writer tasked with generating a final-draft response for one specific section of a federal or Non federal RFP response. You must **adhere strictly** to the provided `current_section_outline`, use real content from `past_performance`, and ensure everything aligns with the `procurement_summary`.

# ---

# ## Critical Output Rules
# **You must follow ALL these rules. Violating any rule = failed response.**

# 1. **Output must include every heading and subheading from the section outline.**
#    - Use Markdown (###) for each `subsection_title`.
#    - Never skip, rename, rephrase, reorder, or remove any heading.
#    - Each heading should appear **only once**.
#    - Never repeat headings later in the content.
#    - Place content **under** the appropriate heading, not inside or above it.

# 2.**Do not copy or slightly reword outline content.**
#    - Treat outline text as **instructional only**, not final content.
#    - Rephrase and expand it to include detailed explanations, metrics, examples, and clear value-adds.

# 3.**Absolutely NO hallucinations.**
#    - Do not invent tools, teams, processes, outcomes, or stats.
#    - You may only use concepts that are directly inferable or supported from:
#      - `current_section_outline`
#      - `past_performance`
#      - `procurement_summary`
#    - If information is missing, use placeholders like:  
#      `[Insert Tool Name]`, `[Insert KPI]`, `[Insert Workflow Step]`.

# 4.**No duplicated or repeated sentences.**
#    - Avoid repeating the same benefit or technical point across subsections.
#    - Use varied language and avoid echoing phrases like "we ensure quality..." across bullets.

# 5. **Use Reflective Mode After Each Section:**
#    - After completing each subsection, pause and verify:
#      - "Did I expand this content meaningfully?"
#      - "Did I reuse or copy any outline sentence?"
#      - "Is this consistent with the procurement goal?"
#      - "Did I reference relevant past performance?"
#      - "Is this too vague or repetitive?"

# ---

# ## Structure-Driven Generation

# Start with the `current_section_outline` (parsed below) and process each subsection one by one.

# ### If Table of Contents (`toc`) is present:
# - Reproduce it **verbatim**, exactly as given in the `toc` section.
# - Do not summarize, interpret, or paraphrase it.

# ### For all `section_title` and `subsection_title` entries:
# - Use proper Markdown heading formatting.
# - Maintain the order and nesting of sections as given.
# - Write original, detailed content under each heading using guidance in the `content` field.

# ---

# ## Evidence-Based Writing Instructions

# For every subsection:

# - **Translate the outline description** into specific methods, steps, and tools.
# - **Back all claims with examples or metrics** from `past_performance`.
# - **Highlight differentiators** that align with the client's mission, RFP goals, and quality standards.
# - **Map your solution** to procurement objectives such as quality, timeliness, scalability, or compliance.
# - **Use industry terms and frameworks** that evaluators will recognize, only if they’re supported.

# ---

# ## Content Sources

# - `current_section_outline`: Fully parsed content and requirements for this section.
# - `past_performance`: Full text of prior performance and achievements.
# - `procurement_summary`: Details of client goals, success criteria, pain points, etc.

# ---

# ## Output Formatting

# - Use **Markdown** for:
#   - Section/subsection titles (`###`)
#   - Bullet points (with `-`)
#   - Tables (in valid Markdown table format, preserved from outline)
# - Ensure all numerical, pricing, or structured data matches outline values **exactly**.
# - Do not approximate or alter figures from the outline.

# ---

# ## Final Output Expectations

# - **No summary or commentary. Only deliver the final content.**
# - **Do not add new headings** unless you are filling a placeholder marked in the outline.
# - **Do not omit or merge any subsections**.
# - **Each `subsection_title` must appear once and only once** as a heading.
# - Treat this like a **ready-to-submit response**—no rough drafts or filler.

# ---

# ## INPUT DATA
# ### Complete Outline
# {full_outline}
# ### Current Section Outline with Complete Content:
# {current_section_outline}

# ### Past Performance:
# {past_performance}

# ### Procurement Summary:
# {procurement_summary}

# BEGIN RESPONSE GENERATION NOW for  Current Section Outline with Complete Content:
# """
#     return prompt

# def get_response_generation_prompt(current_section_outline, past_performance, procurement_summary):
#     prompt = f"""Role: Expert Procurement Response Writer & Subject Matter Specialist
# **Winning Proposal Content Generation:**
# 1. **Precision-Focused Section Development:**
#    - Generate polished, submission-ready content ONLY for the current section
#    - Follow the current section's outline with exact precision
#    - Maintain strict adherence to section boundaries
#    - Deliver client-ready, professional content requiring no additional editing
#    - **Do not reuse or replicate outline text verbatim—rephrase and expand using original, value-added content**
#    - Treat outline content as a baseline. Expand significantly beyond it using technical detail, solution-specific descriptions, implementation steps, metrics, or examples.
#   - Do not assume outline text is comprehensive—**build upon it** as if explaining the concept to a government evaluator with no prior context.
#   - **All generated content, including expansions and details, must be directly inferable or substantiated by the provided `current_section_outline`, `past_performance`, and `procurement_summary`. Do not introduce external information or assumptions not supported by these sources.**
#   - Provide **clear value-added content** that goes **beyond what is mentioned**, drawing on best practices, domain knowledge, and past performance.
#   - When a subsection lacks detailed requirements, **infer likely client needs based on RFP goals** and expand responsibly.


# 2. **Evidence-Based Response Writing:**
#    - Incorporate specific, quantifiable achievements from past performance
#    - Reference relevant capabilities that address explicit RFP requirements
#    - Substantiate claims with concrete examples and metrics
#    - Ensure all content aligns with evaluation criteria
#    - **Do not generate content that contradicts or is not directly supported by the information presented in the `current_section_outline`, `past_performance`, or `procurement_summary`.**

# 3. **Authoritative Technical Content:**
#    - Demonstrate deep subject matter expertise through precise terminology
#    - Present implementation approaches with detailed methodologies
#    - Address technical requirements with specific, actionable solutions
#    - Utilize industry-standard frameworks and methodologies appropriately

# 4. **Comprehensive Value Proposition:**
#    - Articulate clear differentiators throughout the response
#    - Emphasize client-focused outcomes and measurable benefits
#    - Connect proposed solutions to specific procurement objectives
#    - Demonstrate thorough understanding of client requirements

# 5. **Professional Presentation:**
#    - Implement clean, consistent markdown formatting
#    - Utilize headings, bullet points, and tables for optimal readability
#    - Apply emphasis formatting for key differentiators and benefits
#    - Create visually scannable content for evaluator ease-of-review

# 6. **Context-Aware Content Development:**
#    - The current section outline now contains complete content and requirements
#    - Use the detailed content provided in each subsection to understand full context
#    - Maintain consistent messaging with the overall proposal strategy
#    - Create cohesive content that supports the comprehensive win strategy
#    - **For every statement, solution, or claim generated, ensure it can be traced back directly to specific information within the `current_section_outline`, `past_performance`, or `procurement_summary`.**

# 7. **Placeholder Implementation:**
#    - Incorporate clearly marked, descriptive placeholders only when necessary:
#      * [Company Name]
#      * [Technical Specification]
#      * [Point of Contact]
#      * [Performance Metric]
     
# 8. **Past Performance Integration:**
#     - **For any section or subsection directly related to 'Past Performance' as indicated in the current section outline, ensure comprehensive integration of relevant details from the provided `past_performance` content.**
#     - **Extract and present all specific requirements, achievements, and contextual information from the `past_performance` data that directly address the outline's demands for that section.**
#     - **If the outline specifies particular projects, metrics, or types of experience within a past performance section, actively seek and include those details from the `past_performance` source.**
    
# 9. **Table of Contents (ToC) Handling:**
#    - Preserve the Table of Contents exactly as it appears in the current section outline
#    - **Do not rewrite, describe, paraphrase, or summarize the ToC**
#    - The ToC is for structural reference only and should be included in the final content exactly as-is
#    - Do not add any additional narrative, introduction, or generated explanation around the ToC
#    - Use the ToC hierarchy and titles to structure the generated response.
#    - Treat each ToC entry as a required Markdown heading in the final output.
#    - Follow the order and nesting of ToC entries strictly. Do not skip, merge, rename, or reorder any ToC item.
   
# 10. **Table Handling:**
#     - If any table structure exists in the section outline (e.g., pricing tables, task breakdowns), preserve the original table structure by converting it into clean, valid Markdown tables.
#     - Ensure the table content aligns exactly with the original columns and values provided in the outline.
#     - Tables should be rendered in standard Markdown so they are preserved when converting to PDF or DOCX.
#     - **Do not flatten tables into plain bullet points or unstructured text.**
  
# 11. 10. **Structured Data Integrity:**
#    - Preserve all numerical values, tabular formats, and structured content **exactly** as provided in the outline.
#    - **Do not alter, infer, or approximate** any values (e.g., quantities, hours, pricing) when rendering tables.
#    - If a table is present in the outline, **replicate it exactly**, including column order, cell values, and structure.
#    - Do not generate or fill in missing numerical values unless explicitly instructed.
#    - Always assume provided structured data is authoritative.
# **Important Notes:**
# - The current section outline now includes the complete, exact content from the original outline
# - Each subsection contains both the title and the full requirements/specifications
# - Use this comprehensive content to generate detailed, compliant responses
# - No need for additional outline context as all requirements are included in the section

# **Response Requirements:**
# DELIVER FINAL-DRAFT QUALITY CONTENT FOR THE SPECIFIED SECTION ONLY.
# CREATE DETAILED, SUBSTANTIVE CONTENT WITH PROPER TECHNICAL DEPTH.
# IMPLEMENT PROFESSIONAL MARKDOWN FORMATTING FOR EVALUATOR READABILITY.
# PROVIDE COMPLETE SECTION CONTENT WITH NO INTRODUCTORY OR CONCLUDING COMMENTARY.
# WRITE A DETAILED, SEPARATE RESPONSE FOR EVERY SUBSECTION IN THE CURRENT SECTION OUTLINE.
# USE THE EXACT SUBSECTION TITLES AS MARKDOWN HEADINGS.
# ENSURE 100% COVERAGE OF ALL SUBSECTIONS WITH NO OMISSIONS.
# **INCLUDE EVERY HEADING AND SUBHEADING FROM THE PROVIDED SECTION OUTLINE, WITHOUT OMISSION.**
# **MAINTAIN THE ORIGINAL ORDER OF ALL HEADINGS AND SUBHEADINGS AS GIVEN IN THE OUTLINE.**
# **DO NOT SKIP, REMOVE, OR MERGE ANY OUTLINE ELEMENTS.**
# **AVOID REPETITION OF HEADINGS OR SUBHEADINGS IN THE OUTPUT.**
# INCLUDE EACH SUBSECTION TITLE **ONCE ONLY** AS A MARKDOWN HEADING.
# DO NOT RE-INTRODUCE OR RESTATE HEADINGS WITHIN THE BODY TEXT OR BELOW THE INITIAL HEADING.
# IF THE SECTION OUTLINE CONTAINS A TABLE OF CONTENTS, INCLUDE IT EXACTLY AS PROVIDED, WITHOUT ADDING OR REWRITING TEXT AROUND IT.

# **Content Sources:**
# - Current Section Outline with Complete Content: {current_section_outline}
# - Past Performance: {past_performance}
# - Procurement Summary: {procurement_summary}

# """
#     return prompt