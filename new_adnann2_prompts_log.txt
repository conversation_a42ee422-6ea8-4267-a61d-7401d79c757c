====================Prompt Log Start ================================================


=================== Prompt for Section pre-toc: Content Before Table of Contents ====================

Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** Content Before Table of Contents  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: [][A picture containing text, clipart Description automatically
generated][Logo Description automatically generated][A picture
containing shape Description automatically generated][][][][]

Volume I – Technical

“This proposal includes data that shall not be disclosed outside the
Government and shall not be duplicated, used or disclosed in whole or in
part for any purpose other than to evaluate this proposal. If, however,
a Contract is awarded to this Offeror as a result of or in connection
with the submission of this data, the Government shall have the right to
duplicate, use, or disclose the data to the extent provided in the
resulting Contract. This restriction does not limit the Government’s
right to use information contained in this data if it is obtained from
another source without restriction. The data subject to the restriction
is contained in all sheets of this proposal.”

Office of the Comptroller of the Currency (OCC)

Cyber Security Office (CSO)

Cybersecurity Assessment and Compliance (CA&C)

Solicitation Number: 2031JW22Q00022

Due Date: 10:00 AM (EST), October 29, 2021

October 29, 2021

Solicitation Number: 2031JW22Q00022

Agency: Office of the Comptroller of the Currency (OCC)

Subject: Team Greenbrier Response to Cybersecurity Assessment and
Compliance (CA&C)

Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter
referred to as “Team Greenbrier”) are pleased to submit this response to
the Office of the Comptroller of the Currency (OCC), Cybersecurity
Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion
for solving problems and ensuring the sustainability of results through
project leadership extends across multiple industries. Team Greenbrier’s
substantial past performance and reputation for excellence with our
clients attest to the fact that we continually deliver low-risk,
high-quality, value-added strategic support services.

Team Greenbrier has been providing Cybersecurity Assessment and
Compliance services for many years to a large base of clients in the
federal government and commercial space as part of projects that are
similar in size and scope to the work that will be part of the OCC
program. Our deep subject matter expertise in Cybersecurity Assessment,
Compliance, and experience in providing innovative services with proven
program management skills will foster the support needed to ensure the
best quality services.

We are enthusiastic about the opportunity to work with you and eager to
demonstrate immediately the positive impact we can have on your
organization. Team Greenbrier acknowledges receipt of the RFQ and
Amendments and takes no exception to the terms and conditions. Our
company information includes:

The information of teaming partner includes:

+---------------------------------------+------------------------------+
| - Company Name:                       | - DUNS Number:               |
+=======================================+==============================+

We are personally committed to the success of this program and agree
with all terms, conditions, & provisions and to furnish any or all items
included in the solicitation. With our dynamic leadership, supervision,
and more than capable personnel, we are confident that we are ideally
suited to fulfill your needs. If you have any questions or need
additional information, please contact our primary point of contact.

Respectfully,

[]

Scotty Johnson

President

Table of Contents

1 Assumptions 1

1.1 Technical Assumptions 1

1.2 Price Assumptions 1

2 SF 1449 1

3 Acknowledgment of Solicitation Amendments 1

4 Completion of Representations: 1

5 Attestation 1

6 Conflict of Interest Mitigation Plan 1

7 GSA Federal Supply Schedule (FSS) 1

8 Technical/Management Approach (No more than 15 pages) 2

8.1 Methodology/process for testing web applications, financial systems,
network and infrastructure devices and end-user devices, including a
detailed process for manual and automated testing 2

8.2 Methodology for assigning risk ratings to weaknesses discovered
during the assessment process 2

8.3 Process for audit preparation and tracking, distributing, and
responding to data call requests associated with annual external audits
(FISCAM, FISMA and A-123) 2

8.4 Process for integrating the Risk Management Framework (RMF) –
Monitor step with an eGRC tool 2

8.5 Process for automating assessments 2

8.6 Methodology for tracking, reporting and completing all work
identified in the PWS 2

8.7 Staffing plan and approach for obtaining and maintaining individuals
with the qualifications listed in the Key Personnel section 2

9 Key Personnel Resumes (No more than 3 pages per resume) 2

9.1 Project Manager 2

9.2 Cybersecurity Assurance & Compliance Program Support Lead 3

9.3 Security & Privacy Control Assessor Lead 3

9.4 Quality Assurance Lead 4

9.5 Tier 2 eGRC Specialist 4

9.6 Automated Assessment Developer 4

9.7 IT Security Subject Matter Expert 5

10 Past Performance 5

Assumptions

Technical Assumptions

<Insert Assumptions>

Price Assumptions

<Insert Assumptions>

SF 1449

Completed by the Quoter and signed by an authorized official of the
company.

<Insert SF 1449>

Acknowledgment of Solicitation Amendments

<Insert Response>

Completion of Representations:

OCC provision 1052.209-8001, Conflict of Interest Disclosure and
Certification (Feb 2014)

<Insert Reps and Certs>

Attestation

Attest by stating in its quote submission that “All quoted contractor
personnel meet Contractor Qualifications and Key Personnel outlined in
Section II, “Qualifications for Tasks/Key Personnel” in the SOW.

<Insert Response>

Conflict of Interest Mitigation Plan

Provide a description of any actual or potential conflicts of interest
related to this requirement. If the Quoter has identified any actual or
potential conflicts, the Quoter shall provide a mitigation plan. If
Quoter has identified no actual or potential conflicts of interest,
quoter shall provide a statement in writing to that affect (this can be
satisfied with completion of OCC Provision 1052.209-8001). Quoters may
submit a single OCI statement, however, this does not alleviate the
prime contractor’s responsibility to research potential conflicts of any
subcontractors and submit a mitigation strategy, if applicable.

<Insert Response>

GSA Federal Supply Schedule (FSS)

The vendor must explicitly confirm that all proposed services fall under
a vendor’s GSA Federal Supply Schedule (FSS). If the vendor has a
teaming arrangement with another vendor to provide any services in the
vendor’s quotation, please identify the vendor and the GSA FSS number
for those affected CLINs.

<Insert Response>

Technical/Management Approach (No more than 15 pages)

Contractors shall provide a technical/management approach that conveys
an understanding of the requirement and includes:

Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

<Insert Response>

Methodology for assigning risk ratings to weaknesses discovered during the assessment process

<Insert Response>

Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

<Insert Response>

Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

<Insert Response>

Process for automating assessments

<Insert Response>

Methodology for tracking, reporting and completing all work identified in the PWS

<Insert Response>

Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

<Insert Response>

Key Personnel Resumes (No more than 3 pages per resume)

The Quoter shall submit resumes based on the individual’s experience and
certifications, for each of the Key Personnel identified in the SOW. The
Quoter shall also provide a letter of commitment for each proposed key
personnel.

Project Manager

The Project Manager shall meet the following minimum criteria:

- At least eight (8) years of experience in program and project
  management supporting information security or cybersecurity projects
  for the federal government is required.

- A bachelor's degree from an accredited college and a Project
  Management Professional (PMP) or equivalent (as approved by the OCC
  COR) is required.

- Hands-on experience using the Microsoft Office Project is required.

- Experience with NIST Risk Management Framework and Governance, Risk &
  Compliance (GRC) and Information Assurance capabilities/tools (e.g.,
  ServiceNow GRC, RSA Archer, CSAM, Xacta, etc.) is required.

- Certified Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified Information Systems Security
  Professional (CISSP), or Certified Information Security Manager (CISM)
  is required.

<Insert Resume>

Cybersecurity Assurance & Compliance Program Support Lead

The Cybersecurity Assurance and Compliance Program Support Lead shall
meet the following minimum criteria, unless otherwise noted as
preferred:

- At least three (3) years of experience managing security compliance
  program support teams is required.

- At least six (6) years of experience developing RMF documentation in
  the last five years, including but not limited to: Standard Operating
  Procedures (SOPs), system security plans (SSPs), program plans,
  processes, workflows are required.

- At least eight (8) years of Information Security experience is
  required.

- At least two (2) years of experience using eGRC tools is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified in Risk and Information Systems Control
  (CRISC), or Certified Information Security Manager (CISM) is required.

- Experience with ServiceNow GRC CAM tool is preferred.

<Insert Resume>

Security & Privacy Control Assessor Lead

The Security and Privacy Control Assessor Lead shall meet the following
minimum criteria, unless otherwise noted as preferred:

- Three (3) years of experience managing security assessment teams is
  required.

- Six (6) years of experience performing detailed, full-scope technical
  security control testing for each of the component types listed in
  Section C.3.1, including development of security and privacy
  assessment plans is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with the use of eGRC tools is required.
  Experience with ServiceNow is preferred.

- Experience working with Qualys Enterprise, Archer, Nessus, HCL
  AppScan, and technical outputs of the Kali Linux suite is required.

- Advanced knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Ethical Hacker (CEH), Certified Risk and Information Systems Control
  (CRISC), or Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Quality Assurance Lead

The Quality Assurance (QA) Lead shall meet the following minimum
criteria, unless otherwise noted as preferred:

- Three (3) years of managing technical security QA team/SA&A Package
  Independent Validation & Verification (IV&V) is required.

- Six (6) years of experience developing RMF documentation is required.

- Six (6) years of experience conducting security and privacy control
  assessments is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with eGRC tools is required. Experience
  with ServiceNow GRC tool suite, including CAM, is preferred.

- Certified in Certified Information Systems Security Professional
  (CISSP), Certified Risk and Information Systems Control (CRISC), or
  Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Tier 2 eGRC Specialist

The Tier 2 eGRC Specialist shall meet the following minimum criteria,
unless otherwise noted as preferred:

- At least two (2) years of experience with developing/writing advanced
  custom ServiceNow script includes, user interface actions, user
  interface policies, access control lists, client scripts, scheduled
  jobs, data tables and data fields in the last three years is required.

- At least three (3) years of Integrated Risk Management (IRM)/GRC
  implementation experience in the last four years is required; GRC CAM
  experience is preferred. ServiceNow Certified Risk & Compliance
  implementation specialist is preferred.

- At least (2) years of experience configuring and customizing the ITSM
  suite, IT Operations Management suite, and NOW Platform Capabilities
  in the last three years is required.

- At least (2) years of experience with technical components such as
  LDAP, Web Services, REST, SOAP, APIs, XML, JavaScript in the last
  three years is required.

- At least (2) years of experience with ITILv3 Service Management
  processes in the last three years is required.

- At least three (3) years of Administrator/Developer experience in the
  last four years is required; in addition, GRC ServiceNow Certified
  Developer is preferred.

- At least two (2) years of ServiceNow Implementation Specialist
  experience in the last three years is required; in addition, Certified
  ServiceNow Implementation Specialist is preferred.

<Insert Resume>

Automated Assessment Developer

The Automated Assessment Developer shall meet the following minimum
criteria:

- Five (5) years of experience as a software developer is required,

- Three (3) years of experience integrating cybersecurity tools,
  including Qualys, RSA Archer, and Splunk with ServiceNow GRC and
  ServiceNow formats and languages listed in Task C.2.7 is preferred.

- Bachelor of Science degree in Computer Science is required.

<Insert Resume>

IT Security Subject Matter Expert

The IT Security Subject-Matter Expert (SME), who handles areas requiring
elevated technical security skills (e.g., risk analysis of cutting-edge
technology, or high visibility project that requires a SME review due to
quick turn around with accurate results, etc.), and technical escalation
concerns that may arise in the implementation of new IT security and/or
compliance program requirements, security assessments, technical SA&A
package evaluations, etc., shall meet the following minimum criteria,
unless otherwise noted as preferred:

- Ten (10) years of IT security experience is required.

- Five (5) years of experience performing detailed, full-scope technical
  control testing for the component types listed in Section C.3.1 of
  this SOW including development security assessment plans is required.

- In-depth knowledge of and experience in implementing and using GRC
  platforms is required. Experience with ServiceNow GRC tool suite,
  including CAM preferred.

- Hands-on experience working with at least four (4) of the following
  seven (7) technology products: Forescout CounterAct, ArcSight, HCL
  BigFix, Sailpoint, CyberArk, RES, and Splunk is required.

- Extensive knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Bachelor of Science degree in Computer Science, Cybersecurity, or
  Information Systems. is required.

- Certified Information Systems Security Professional (CISSP) is
  required.

- Certified Cloud Security Professional (CCSP) is preferred.

<Insert Resume>

Past Performance

The Quoter shall request that references fill out and return three (3)
Past Performance Questionnaire Survey (Attachment 3). Quoters may not
submit the completed survey forms to the OCC Contract Specialist on
behalf of their references; instead, the completed forms shall be
returned directly by the referenced customers to the OCC Contract
Specialist, per the information provided on the form. The forms are due
to the OCC no later than the specified deadline for submitting
quotations in response to this RFQ. The OCC may also obtain and consider
any other relevant information in evaluating past performance, including
information contained in Contractor Performance Assessment Reporting
System (CPAR).
  
- Section Outline: Content Before Table of Contents  
- RFP Summary: 
Proposal Info
•  Solicitation Number: RFQ 2031JW22Q00022 [Page 17]
•  NAICS Code: 541519 [Page 51]
•  Name/Title: Cybersecurity Assessment and Compliance (CA&C) Support [Page 1]
•  Solicitation Type: RFQ [Page 17]
•  Department/Agency Name: Comptroller of the Currency [Page 1]
•  Inquiries/Questions Due Date and Time: 12:00 PM (EST), October 12, 2021 [Page
51]
•  Proposal Due Date and Time: 10:00 AM (EST), October 29, 2021 [Page 50]
•  Mode of Submission: eBuy [Page 50]
•  Place of Performance: Online [Page 50]
•  Point of Contact (POC):
○  Primary: <EMAIL> [Page 51]
○  Secondary: <EMAIL> [Page 51]
•  Set Aside: Small Business [Page 1]
•  Period of Performance:
○  Base Period: 12 months, 01/03/2022 to 01/02/2023 [Page 2]
○  Option Period 1: 12 months, 01/03/2023 to 01/02/2024 [Page 3]
○  Option Period 2: 12 months, 01/03/2024 to 01/02/2025 [Page 4]
○  Option Period 3: 12 months, 01/03/2025 to 01/02/2026 [Page 5]
○  Option Period 4: 12 months, 01/03/2026 to 01/02/2027 [Page 5]
•  Key Personnel:
○  Project Manager [Page 22]
○  Cybersecurity Assurance and Compliance Program Support Lead [Page 22]
○  Security and Privacy Control Assessor Lead [Page 23]
○  Quality Assurance Lead [Page 24]
○  Tier 2 eGRC Specialist [Page 23]
○  Automated Assessment Developer [Page 23]
○  IT Security Subject Matter Expert [Page 23]
•  Security Clearance Requirements: N/A [Page 49]
•  Task Order Type: N/A [Page 50]
Purpose
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). [Section II - Statement of Work (SOW)] The CA&C
Program includes agency-level oversight for compliance with the Risk Management
Framework (RMF), Audit Liaison, and execution of Assessment and Authorization steps of the
RMF. The scope of work shall comprise: Project Management and Reporting; Cyber
Assurance and Compliance (CA&C) Program Support, including, Audit Liaison, Compliance
Oversight activities, CA&C program security documentation, and technical support for
automation and program tools; Security & Privacy Control Assessments of new and existing
OCC systems; Quality Assurance; and Administrative and IT Security Subject Matter Expert
Support. [Section II - Statement of Work (SOW)]
Scope
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). The CA&C Program includes agency-level oversight
for compliance with the Risk Management Framework (RMF), Audit Liaison, and execution of
Assessment and Authorization steps of the RMF. The scope of work shall comprise: Project
Management and Reporting; Cyber Assurance and Compliance (CA&C) Program Support,
including, Audit Liaison, Compliance Oversight activities, CA&C program security
documentation, and technical support for automation and program tools; Security & Privacy
Control Assessments of new and existing OCC systems; Quality Assurance; and
Administrative and IT Security Subject Matter Expert Support.
Task Areas Overview
•  C.1 Project Management and Reporting
•  C.2 Cyber Assurance and Compliance Program Support
•  C.3 Security and Privacy Control Assessments
•  C.4 Quality Assurance
•  C.5 Administrative Support
•  C.6 IT Security Subject Matter Expert Support

  
- Knowledge Base: +---------------------------------------------------------------------------------------------------------------------------+
| ABC Government Solutions, Inc                                                                                             |
+==================+==============+==============+==============+==============+==============+==============+==============+
| Contracting Agency / Business   | Department of Veterans Affairs                                                          |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Applicable Contract Number      |                             | Total Dollar Value          |                             |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Contract Name                   | FSS Program Management Support (HMS Technologies)                                       |
+---------------------------------+-----------------------------+--------------------------------------------+--------------+
| Contract Type                   | FFP                         | Place of Performance                       | Remote       |
+------------------+--------------+--------------+--------------+--------------+-----------------------------+--------------+
| Prime or         | Subcontractor               | Period of Performance       |                                            |
| Subcontractor    |                             |                             |                                            |
+------------------+-----------------------------+-----------------------------+--------------------------------------------+
| POC              |                                                                                                        |
+------------------+--------------------------------------------------------------------------------------------------------+
| Description of Services                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in      |
|   order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS             |
|   requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness       |
|   Information Security Protection (CRISP) project.                                                                        |
|                                                                                                                           |
| - We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones  |
|   and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities      |
|   associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones    |
|   (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track     |
|   progress status of VA wide tasks.                                                                                       |
+---------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - FSS SHAREPOINT SUPPORT                                                                                                  |
|                                                                                                                           |
| ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for             |
| improvements in FSS business operations methods to achieve improvements in FSS business operations which included:        |
|                                                                                                                           |
| 1.  Business process or procedures changes                                                                                |
|                                                                                                                           |
| 2.  Application of pertinent Technology (e.g., SharePoint tracking)                                                       |
|                                                                                                                           |
| 3.  Ability to utilize SharePoint applications to improve business operations                                             |
|                                                                                                                           |
| We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation)   |
| that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As   |
| part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with   |
| links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other       |
| similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.                     |
|                                                                                                                           |
| - FSS CRISP PROGRAM SUPPORT                                                                                               |
|                                                                                                                           |
| ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components,     |
| milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of  |
| CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.   |
|                                                                                                                           |
| We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts      |
| (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with |
| CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, |
| action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress         |
| upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided       |
| weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications    |
| System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues,   |
| risks and accomplishments associated with FSS CRISP sustainment.                                                          |
|                                                                                                                           |
| We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS    |
| Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks,    |
| and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS         |
| Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues     |
| associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All  |
| weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any     |
| distribution.                                                                                                             |
|                                                                                                                           |
| - GOVERNANCE SUPPORT                                                                                                      |
|                                                                                                                           |
| ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to      |
| include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and    |
| workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments,  |
| delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and       |
| potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which    |
| measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics |
| were maintained and continuously revised, by us, as the need for change arises. We provided project governance management |
| expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported   |
| the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization,    |
| and present it to FSS leadership for review and approval.                                                                 |
|                                                                                                                           |
| As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and     |
| facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned   |
| with FSS mission and goals.                                                                                               |
|                                                                                                                           |
| - FSS REQUIREMENTS ELABORATION PLANNING SUPPORT                                                                           |
|                                                                                                                           |
| As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements    |
| Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan     |
| includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and    |
| OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS     |
| Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in   |
| the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and      |
| channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and          |
| communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and      |
| approval from the COR/FSS VA PM.                                                                                          |
|                                                                                                                           |
| We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all  |
| FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future            |
| activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided        |
| communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.                           |
|                                                                                                                           |
| - FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which    |
|   includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet        |
|   postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor      |
|   these correspondence/communication products as needed.                                                                  |
|                                                                                                                           |
| - Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders       |
|   understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities |
|   in implementing medical/special purpose device security.                                                                |
|                                                                                                                           |
| - FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements  |
|   for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance  |
|   to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; |
|   FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and         |
|   Vanguard Magazine.                                                                                                      |
|                                                                                                                           |
| - FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS        |
|   Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, |
|   support topic research, capture notes, poll the group on topics, and conduct live meetings.                             |
|                                                                                                                           |
| - FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security    |
|   matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and |
|   ad hoc training on new security initiatives.                                                                            |
+---------------------------------------------------------------------------------------------------------------------------+

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC                                                                                                                                                                     |
+================+================+================+================+================+================+================+================+================+================+
| Contracting Agency / Business                    | Department of Veterans Affairs                                                                                       |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Applicable Contract Number                       |                                                  | Total Dollar Value                               |                |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Contract Name                                    | Security Control Assessments                                                                                         |
+----------------+---------------------------------+---------------------------------+--------------------------------------------------+---------------------------------+
| Contract Type  | Firm Fixed Price (FFP)                                            | Place of Performance                             | Remote Locations                |
+----------------+----------------+---------------------------------+----------------+---------------------------------+----------------+---------------------------------+
| Prime or Subcontractor          |                                 | Period of Performance                            | 12 months from date of award, with four 12-month |
|                                 |                                 |                                                  | option periods.                                  |
+---------------------------------+---------------------------------+--------------------------------------------------+--------------------------------------------------+
| POC                             |                                                                                                                                       |
+---------------------------------+---------------------------------------------------------------------------------------------------------------------------------------+
| Background                                                                                                                                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and      |
| services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services  |
| to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and      |
| compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals.                                                |
|                                                                                                                                                                         |
| During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector       |
| General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls,              |
| configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously         |
| identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support        |
| Federal regulatory requirements and the Federal Government oversight and audit community.                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Scope of Work                                                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC performed:                                                                                                                                                          |
|                                                                                                                                                                         |
| 1.  Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented   |
|     correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting  |
|     security requirements.                                                                                                                                              |
|                                                                                                                                                                         |
| a)  Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the      |
|     test.                                                                                                                                                               |
|                                                                                                                                                                         |
| b)  Execute the SAP.                                                                                                                                                    |
|                                                                                                                                                                         |
| c)  Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).                           |
|                                                                                                                                                                         |
| 2.  Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within   |
|     or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall          |
|     effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with      |
|     respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the       |
|     information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above               |
|     responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.      |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                                                                 |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Contractor Independent Assessment                                                                                                                                       |
|                                                                                                                                                                         |
| ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information    |
| systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because  |
| SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an        |
| assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their         |
| environment of operation and recommend corrective actions to address identified vulnerabilities.                                                                        |
|                                                                                                                                                                         |
| ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization     |
| (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).   |
|                                                                                                                                                                         |
| ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed.                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment Preparation                                                                                                                                 |
|                                                                                                                                                                         |
| - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements |
|   captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to   |
|   develop a System Rules of Engagement (ROE) Agreement.                                                                                                                 |
|                                                                                                                                                                         |
| The ROE must correctly identify the following:                                                                                                                          |
|                                                                                                                                                                         |
| 1)  Scope of testing                                                                                                                                                    |
|                                                                                                                                                                         |
| a)  ABC only tested for controls within scope of system type, location, and within the appropriate management control                                                   |
|                                                                                                                                                                         |
| 2)  Network ranges being assessed                                                                                                                                       |
|                                                                                                                                                                         |
| 3)  System components being assessed                                                                                                                                    |
|                                                                                                                                                                         |
| 4)  Locations being assessed                                                                                                                                            |
|                                                                                                                                                                         |
| 5)  SCA and all members conducting assessments including systems being used                                                                                             |
|                                                                                                                                                                         |
| 6)  Assessment type and method                                                                                                                                          |
|                                                                                                                                                                         |
| 7)  Tools used for the assessment                                                                                                                                       |
|                                                                                                                                                                         |
| 8)  A detailed list of artifacts or evidence not of record with the system and needed to support the SCA                                                                |
|                                                                                                                                                                         |
| - ABC developed and submitted a SCA Test Plan which:                                                                                                                    |
|                                                                                                                                                                         |
| 1.  Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance    |
|     reviews), resources, and milestones for the system being tested.                                                                                                    |
|                                                                                                                                                                         |
| 2.  Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities.                         |
|                                                                                                                                                                         |
| 3.  Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA.                     |
|                                                                                                                                                                         |
| 4.  Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were   |
|     used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.                                                                   |
|                                                                                                                                                                         |
| 5.  Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all          |
|     necessary parties.                                                                                                                                                  |
|                                                                                                                                                                         |
| - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and       |
|   procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems       |
|   needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security      |
|   categorization.                                                                                                                                                       |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment                                                                                                                                             |
|                                                                                                                                                                         |
| - ABC performed the SCA according to the processes and procedures described in the SAP.                                                                                 |
|                                                                                                                                                                         |
| - ABC completed the following communication and reporting activities:                                                                                                   |
|                                                                                                                                                                         |
| 1.  SCA Pre-Site Meeting                                                                                                                                                |
|                                                                                                                                                                         |
| 2.  System Component Assessment Daily Status                                                                                                                            |
|                                                                                                                                                                         |
| 3.  Weekly Status Report                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  SCA Out-Brief Meeting                                                                                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Assessment Report                                                                                                                                              |
|                                                                                                                                                                         |
| - ABC developed the Security Assessment Report to include the following:                                                                                                |
|                                                                                                                                                                         |
| 1.  Documentation of each SCA                                                                                                                                           |
|                                                                                                                                                                         |
| 2.  Assessment test objectives as identified in NIST SP 800-53A                                                                                                         |
|                                                                                                                                                                         |
| 3.  Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A                                                                             |
|                                                                                                                                                                         |
| 4.  All software and hardware components assessed                                                                                                                       |
|                                                                                                                                                                         |
| 5.  Sequential, step-by-step assessment procedures for testing each test objective                                                                                      |
|                                                                                                                                                                         |
| 6.  Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment      |
|                                                                                                                                                                         |
| 7.  Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices        |
|                                                                                                                                                                         |
| 8.  Rationale for any system or device in the inventory not directly tested                                                                                             |
|                                                                                                                                                                         |
| 9.  Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed,     |
|     location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark                                         |
|                                                                                                                                                                         |
| 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or     |
|     “Other Than Satisfied” determination                                                                                                                                |
|                                                                                                                                                                         |
| 11. Results of Findings to be documented in the OCS POA&M Import Template (data                                                                                         |
|                                                                                                                                                                         |
| defined file in Excel format)                                                                                                                                           |
|                                                                                                                                                                         |
| 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual |
|     results for each system component type                                                                                                                              |
|                                                                                                                                                                         |
| 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO                                                         |
|                                                                                                                                                                         |
| - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and      |
|   reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)                              |
|                                                                                                                                                                         |
| - ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA |
|   Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned    |
|   were collected from the following individuals:                                                                                                                        |
|                                                                                                                                                                         |
| 1.  AO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 2.  SO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 3.  ISSO                                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  IT System Manager/System Steward                                                                                                                                    |
|                                                                                                                                                                         |
| 5.  Security Controls Assessor                                                                                                                                          |
|                                                                                                                                                                         |
| 6.  VA Program Manager                                                                                                                                                  |
|                                                                                                                                                                         |
| 7.  COR                                                                                                                                                                 |
|                                                                                                                                                                         |
| 8.  CO                                                                                                                                                                  |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Ongoing Security Control Assessments                                                                                                                                    |
|                                                                                                                                                                         |
| - ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks                           |
|                                                                                                                                                                         |
| - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.                                                             |
|                                                                                                                                                                         |
| - We performed all required communications and reporting activities as required by RMF Tasks                                                                            |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+



=================== Prompt for Section toc: Table of Contents ====================

Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** Table of Contents  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: [][A picture containing text, clipart Description automatically
generated][Logo Description automatically generated][A picture
containing shape Description automatically generated][][][][]

Volume I – Technical

“This proposal includes data that shall not be disclosed outside the
Government and shall not be duplicated, used or disclosed in whole or in
part for any purpose other than to evaluate this proposal. If, however,
a Contract is awarded to this Offeror as a result of or in connection
with the submission of this data, the Government shall have the right to
duplicate, use, or disclose the data to the extent provided in the
resulting Contract. This restriction does not limit the Government’s
right to use information contained in this data if it is obtained from
another source without restriction. The data subject to the restriction
is contained in all sheets of this proposal.”

Office of the Comptroller of the Currency (OCC)

Cyber Security Office (CSO)

Cybersecurity Assessment and Compliance (CA&C)

Solicitation Number: 2031JW22Q00022

Due Date: 10:00 AM (EST), October 29, 2021

October 29, 2021

Solicitation Number: 2031JW22Q00022

Agency: Office of the Comptroller of the Currency (OCC)

Subject: Team Greenbrier Response to Cybersecurity Assessment and
Compliance (CA&C)

Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter
referred to as “Team Greenbrier”) are pleased to submit this response to
the Office of the Comptroller of the Currency (OCC), Cybersecurity
Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion
for solving problems and ensuring the sustainability of results through
project leadership extends across multiple industries. Team Greenbrier’s
substantial past performance and reputation for excellence with our
clients attest to the fact that we continually deliver low-risk,
high-quality, value-added strategic support services.

Team Greenbrier has been providing Cybersecurity Assessment and
Compliance services for many years to a large base of clients in the
federal government and commercial space as part of projects that are
similar in size and scope to the work that will be part of the OCC
program. Our deep subject matter expertise in Cybersecurity Assessment,
Compliance, and experience in providing innovative services with proven
program management skills will foster the support needed to ensure the
best quality services.

We are enthusiastic about the opportunity to work with you and eager to
demonstrate immediately the positive impact we can have on your
organization. Team Greenbrier acknowledges receipt of the RFQ and
Amendments and takes no exception to the terms and conditions. Our
company information includes:

The information of teaming partner includes:

+---------------------------------------+------------------------------+
| - Company Name:                       | - DUNS Number:               |
+=======================================+==============================+

We are personally committed to the success of this program and agree
with all terms, conditions, & provisions and to furnish any or all items
included in the solicitation. With our dynamic leadership, supervision,
and more than capable personnel, we are confident that we are ideally
suited to fulfill your needs. If you have any questions or need
additional information, please contact our primary point of contact.

Respectfully,

[]

Scotty Johnson

President

Table of Contents

1 Assumptions 1

1.1 Technical Assumptions 1

1.2 Price Assumptions 1

2 SF 1449 1

3 Acknowledgment of Solicitation Amendments 1

4 Completion of Representations: 1

5 Attestation 1

6 Conflict of Interest Mitigation Plan 1

7 GSA Federal Supply Schedule (FSS) 1

8 Technical/Management Approach (No more than 15 pages) 2

8.1 Methodology/process for testing web applications, financial systems,
network and infrastructure devices and end-user devices, including a
detailed process for manual and automated testing 2

8.2 Methodology for assigning risk ratings to weaknesses discovered
during the assessment process 2

8.3 Process for audit preparation and tracking, distributing, and
responding to data call requests associated with annual external audits
(FISCAM, FISMA and A-123) 2

8.4 Process for integrating the Risk Management Framework (RMF) –
Monitor step with an eGRC tool 2

8.5 Process for automating assessments 2

8.6 Methodology for tracking, reporting and completing all work
identified in the PWS 2

8.7 Staffing plan and approach for obtaining and maintaining individuals
with the qualifications listed in the Key Personnel section 2

9 Key Personnel Resumes (No more than 3 pages per resume) 2

9.1 Project Manager 2

9.2 Cybersecurity Assurance & Compliance Program Support Lead 3

9.3 Security & Privacy Control Assessor Lead 3

9.4 Quality Assurance Lead 4

9.5 Tier 2 eGRC Specialist 4

9.6 Automated Assessment Developer 4

9.7 IT Security Subject Matter Expert 5

10 Past Performance 5

Assumptions

Technical Assumptions

<Insert Assumptions>

Price Assumptions

<Insert Assumptions>

SF 1449

Completed by the Quoter and signed by an authorized official of the
company.

<Insert SF 1449>

Acknowledgment of Solicitation Amendments

<Insert Response>

Completion of Representations:

OCC provision 1052.209-8001, Conflict of Interest Disclosure and
Certification (Feb 2014)

<Insert Reps and Certs>

Attestation

Attest by stating in its quote submission that “All quoted contractor
personnel meet Contractor Qualifications and Key Personnel outlined in
Section II, “Qualifications for Tasks/Key Personnel” in the SOW.

<Insert Response>

Conflict of Interest Mitigation Plan

Provide a description of any actual or potential conflicts of interest
related to this requirement. If the Quoter has identified any actual or
potential conflicts, the Quoter shall provide a mitigation plan. If
Quoter has identified no actual or potential conflicts of interest,
quoter shall provide a statement in writing to that affect (this can be
satisfied with completion of OCC Provision 1052.209-8001). Quoters may
submit a single OCI statement, however, this does not alleviate the
prime contractor’s responsibility to research potential conflicts of any
subcontractors and submit a mitigation strategy, if applicable.

<Insert Response>

GSA Federal Supply Schedule (FSS)

The vendor must explicitly confirm that all proposed services fall under
a vendor’s GSA Federal Supply Schedule (FSS). If the vendor has a
teaming arrangement with another vendor to provide any services in the
vendor’s quotation, please identify the vendor and the GSA FSS number
for those affected CLINs.

<Insert Response>

Technical/Management Approach (No more than 15 pages)

Contractors shall provide a technical/management approach that conveys
an understanding of the requirement and includes:

Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

<Insert Response>

Methodology for assigning risk ratings to weaknesses discovered during the assessment process

<Insert Response>

Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

<Insert Response>

Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

<Insert Response>

Process for automating assessments

<Insert Response>

Methodology for tracking, reporting and completing all work identified in the PWS

<Insert Response>

Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

<Insert Response>

Key Personnel Resumes (No more than 3 pages per resume)

The Quoter shall submit resumes based on the individual’s experience and
certifications, for each of the Key Personnel identified in the SOW. The
Quoter shall also provide a letter of commitment for each proposed key
personnel.

Project Manager

The Project Manager shall meet the following minimum criteria:

- At least eight (8) years of experience in program and project
  management supporting information security or cybersecurity projects
  for the federal government is required.

- A bachelor's degree from an accredited college and a Project
  Management Professional (PMP) or equivalent (as approved by the OCC
  COR) is required.

- Hands-on experience using the Microsoft Office Project is required.

- Experience with NIST Risk Management Framework and Governance, Risk &
  Compliance (GRC) and Information Assurance capabilities/tools (e.g.,
  ServiceNow GRC, RSA Archer, CSAM, Xacta, etc.) is required.

- Certified Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified Information Systems Security
  Professional (CISSP), or Certified Information Security Manager (CISM)
  is required.

<Insert Resume>

Cybersecurity Assurance & Compliance Program Support Lead

The Cybersecurity Assurance and Compliance Program Support Lead shall
meet the following minimum criteria, unless otherwise noted as
preferred:

- At least three (3) years of experience managing security compliance
  program support teams is required.

- At least six (6) years of experience developing RMF documentation in
  the last five years, including but not limited to: Standard Operating
  Procedures (SOPs), system security plans (SSPs), program plans,
  processes, workflows are required.

- At least eight (8) years of Information Security experience is
  required.

- At least two (2) years of experience using eGRC tools is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified in Risk and Information Systems Control
  (CRISC), or Certified Information Security Manager (CISM) is required.

- Experience with ServiceNow GRC CAM tool is preferred.

<Insert Resume>

Security & Privacy Control Assessor Lead

The Security and Privacy Control Assessor Lead shall meet the following
minimum criteria, unless otherwise noted as preferred:

- Three (3) years of experience managing security assessment teams is
  required.

- Six (6) years of experience performing detailed, full-scope technical
  security control testing for each of the component types listed in
  Section C.3.1, including development of security and privacy
  assessment plans is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with the use of eGRC tools is required.
  Experience with ServiceNow is preferred.

- Experience working with Qualys Enterprise, Archer, Nessus, HCL
  AppScan, and technical outputs of the Kali Linux suite is required.

- Advanced knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Ethical Hacker (CEH), Certified Risk and Information Systems Control
  (CRISC), or Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Quality Assurance Lead

The Quality Assurance (QA) Lead shall meet the following minimum
criteria, unless otherwise noted as preferred:

- Three (3) years of managing technical security QA team/SA&A Package
  Independent Validation & Verification (IV&V) is required.

- Six (6) years of experience developing RMF documentation is required.

- Six (6) years of experience conducting security and privacy control
  assessments is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with eGRC tools is required. Experience
  with ServiceNow GRC tool suite, including CAM, is preferred.

- Certified in Certified Information Systems Security Professional
  (CISSP), Certified Risk and Information Systems Control (CRISC), or
  Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Tier 2 eGRC Specialist

The Tier 2 eGRC Specialist shall meet the following minimum criteria,
unless otherwise noted as preferred:

- At least two (2) years of experience with developing/writing advanced
  custom ServiceNow script includes, user interface actions, user
  interface policies, access control lists, client scripts, scheduled
  jobs, data tables and data fields in the last three years is required.

- At least three (3) years of Integrated Risk Management (IRM)/GRC
  implementation experience in the last four years is required; GRC CAM
  experience is preferred. ServiceNow Certified Risk & Compliance
  implementation specialist is preferred.

- At least (2) years of experience configuring and customizing the ITSM
  suite, IT Operations Management suite, and NOW Platform Capabilities
  in the last three years is required.

- At least (2) years of experience with technical components such as
  LDAP, Web Services, REST, SOAP, APIs, XML, JavaScript in the last
  three years is required.

- At least (2) years of experience with ITILv3 Service Management
  processes in the last three years is required.

- At least three (3) years of Administrator/Developer experience in the
  last four years is required; in addition, GRC ServiceNow Certified
  Developer is preferred.

- At least two (2) years of ServiceNow Implementation Specialist
  experience in the last three years is required; in addition, Certified
  ServiceNow Implementation Specialist is preferred.

<Insert Resume>

Automated Assessment Developer

The Automated Assessment Developer shall meet the following minimum
criteria:

- Five (5) years of experience as a software developer is required,

- Three (3) years of experience integrating cybersecurity tools,
  including Qualys, RSA Archer, and Splunk with ServiceNow GRC and
  ServiceNow formats and languages listed in Task C.2.7 is preferred.

- Bachelor of Science degree in Computer Science is required.

<Insert Resume>

IT Security Subject Matter Expert

The IT Security Subject-Matter Expert (SME), who handles areas requiring
elevated technical security skills (e.g., risk analysis of cutting-edge
technology, or high visibility project that requires a SME review due to
quick turn around with accurate results, etc.), and technical escalation
concerns that may arise in the implementation of new IT security and/or
compliance program requirements, security assessments, technical SA&A
package evaluations, etc., shall meet the following minimum criteria,
unless otherwise noted as preferred:

- Ten (10) years of IT security experience is required.

- Five (5) years of experience performing detailed, full-scope technical
  control testing for the component types listed in Section C.3.1 of
  this SOW including development security assessment plans is required.

- In-depth knowledge of and experience in implementing and using GRC
  platforms is required. Experience with ServiceNow GRC tool suite,
  including CAM preferred.

- Hands-on experience working with at least four (4) of the following
  seven (7) technology products: Forescout CounterAct, ArcSight, HCL
  BigFix, Sailpoint, CyberArk, RES, and Splunk is required.

- Extensive knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Bachelor of Science degree in Computer Science, Cybersecurity, or
  Information Systems. is required.

- Certified Information Systems Security Professional (CISSP) is
  required.

- Certified Cloud Security Professional (CCSP) is preferred.

<Insert Resume>

Past Performance

The Quoter shall request that references fill out and return three (3)
Past Performance Questionnaire Survey (Attachment 3). Quoters may not
submit the completed survey forms to the OCC Contract Specialist on
behalf of their references; instead, the completed forms shall be
returned directly by the referenced customers to the OCC Contract
Specialist, per the information provided on the form. The forms are due
to the OCC no later than the specified deadline for submitting
quotations in response to this RFQ. The OCC may also obtain and consider
any other relevant information in evaluating past performance, including
information contained in Contractor Performance Assessment Reporting
System (CPAR).
  
- Section Outline: Table of Contents  
- RFP Summary: 
Proposal Info
•  Solicitation Number: RFQ 2031JW22Q00022 [Page 17]
•  NAICS Code: 541519 [Page 51]
•  Name/Title: Cybersecurity Assessment and Compliance (CA&C) Support [Page 1]
•  Solicitation Type: RFQ [Page 17]
•  Department/Agency Name: Comptroller of the Currency [Page 1]
•  Inquiries/Questions Due Date and Time: 12:00 PM (EST), October 12, 2021 [Page
51]
•  Proposal Due Date and Time: 10:00 AM (EST), October 29, 2021 [Page 50]
•  Mode of Submission: eBuy [Page 50]
•  Place of Performance: Online [Page 50]
•  Point of Contact (POC):
○  Primary: <EMAIL> [Page 51]
○  Secondary: <EMAIL> [Page 51]
•  Set Aside: Small Business [Page 1]
•  Period of Performance:
○  Base Period: 12 months, 01/03/2022 to 01/02/2023 [Page 2]
○  Option Period 1: 12 months, 01/03/2023 to 01/02/2024 [Page 3]
○  Option Period 2: 12 months, 01/03/2024 to 01/02/2025 [Page 4]
○  Option Period 3: 12 months, 01/03/2025 to 01/02/2026 [Page 5]
○  Option Period 4: 12 months, 01/03/2026 to 01/02/2027 [Page 5]
•  Key Personnel:
○  Project Manager [Page 22]
○  Cybersecurity Assurance and Compliance Program Support Lead [Page 22]
○  Security and Privacy Control Assessor Lead [Page 23]
○  Quality Assurance Lead [Page 24]
○  Tier 2 eGRC Specialist [Page 23]
○  Automated Assessment Developer [Page 23]
○  IT Security Subject Matter Expert [Page 23]
•  Security Clearance Requirements: N/A [Page 49]
•  Task Order Type: N/A [Page 50]
Purpose
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). [Section II - Statement of Work (SOW)] The CA&C
Program includes agency-level oversight for compliance with the Risk Management
Framework (RMF), Audit Liaison, and execution of Assessment and Authorization steps of the
RMF. The scope of work shall comprise: Project Management and Reporting; Cyber
Assurance and Compliance (CA&C) Program Support, including, Audit Liaison, Compliance
Oversight activities, CA&C program security documentation, and technical support for
automation and program tools; Security & Privacy Control Assessments of new and existing
OCC systems; Quality Assurance; and Administrative and IT Security Subject Matter Expert
Support. [Section II - Statement of Work (SOW)]
Scope
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). The CA&C Program includes agency-level oversight
for compliance with the Risk Management Framework (RMF), Audit Liaison, and execution of
Assessment and Authorization steps of the RMF. The scope of work shall comprise: Project
Management and Reporting; Cyber Assurance and Compliance (CA&C) Program Support,
including, Audit Liaison, Compliance Oversight activities, CA&C program security
documentation, and technical support for automation and program tools; Security & Privacy
Control Assessments of new and existing OCC systems; Quality Assurance; and
Administrative and IT Security Subject Matter Expert Support.
Task Areas Overview
•  C.1 Project Management and Reporting
•  C.2 Cyber Assurance and Compliance Program Support
•  C.3 Security and Privacy Control Assessments
•  C.4 Quality Assurance
•  C.5 Administrative Support
•  C.6 IT Security Subject Matter Expert Support

  
- Knowledge Base: +---------------------------------------------------------------------------------------------------------------------------+
| ABC Government Solutions, Inc                                                                                             |
+==================+==============+==============+==============+==============+==============+==============+==============+
| Contracting Agency / Business   | Department of Veterans Affairs                                                          |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Applicable Contract Number      |                             | Total Dollar Value          |                             |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Contract Name                   | FSS Program Management Support (HMS Technologies)                                       |
+---------------------------------+-----------------------------+--------------------------------------------+--------------+
| Contract Type                   | FFP                         | Place of Performance                       | Remote       |
+------------------+--------------+--------------+--------------+--------------+-----------------------------+--------------+
| Prime or         | Subcontractor               | Period of Performance       |                                            |
| Subcontractor    |                             |                             |                                            |
+------------------+-----------------------------+-----------------------------+--------------------------------------------+
| POC              |                                                                                                        |
+------------------+--------------------------------------------------------------------------------------------------------+
| Description of Services                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in      |
|   order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS             |
|   requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness       |
|   Information Security Protection (CRISP) project.                                                                        |
|                                                                                                                           |
| - We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones  |
|   and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities      |
|   associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones    |
|   (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track     |
|   progress status of VA wide tasks.                                                                                       |
+---------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - FSS SHAREPOINT SUPPORT                                                                                                  |
|                                                                                                                           |
| ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for             |
| improvements in FSS business operations methods to achieve improvements in FSS business operations which included:        |
|                                                                                                                           |
| 1.  Business process or procedures changes                                                                                |
|                                                                                                                           |
| 2.  Application of pertinent Technology (e.g., SharePoint tracking)                                                       |
|                                                                                                                           |
| 3.  Ability to utilize SharePoint applications to improve business operations                                             |
|                                                                                                                           |
| We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation)   |
| that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As   |
| part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with   |
| links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other       |
| similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.                     |
|                                                                                                                           |
| - FSS CRISP PROGRAM SUPPORT                                                                                               |
|                                                                                                                           |
| ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components,     |
| milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of  |
| CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.   |
|                                                                                                                           |
| We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts      |
| (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with |
| CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, |
| action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress         |
| upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided       |
| weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications    |
| System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues,   |
| risks and accomplishments associated with FSS CRISP sustainment.                                                          |
|                                                                                                                           |
| We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS    |
| Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks,    |
| and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS         |
| Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues     |
| associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All  |
| weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any     |
| distribution.                                                                                                             |
|                                                                                                                           |
| - GOVERNANCE SUPPORT                                                                                                      |
|                                                                                                                           |
| ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to      |
| include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and    |
| workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments,  |
| delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and       |
| potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which    |
| measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics |
| were maintained and continuously revised, by us, as the need for change arises. We provided project governance management |
| expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported   |
| the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization,    |
| and present it to FSS leadership for review and approval.                                                                 |
|                                                                                                                           |
| As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and     |
| facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned   |
| with FSS mission and goals.                                                                                               |
|                                                                                                                           |
| - FSS REQUIREMENTS ELABORATION PLANNING SUPPORT                                                                           |
|                                                                                                                           |
| As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements    |
| Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan     |
| includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and    |
| OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS     |
| Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in   |
| the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and      |
| channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and          |
| communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and      |
| approval from the COR/FSS VA PM.                                                                                          |
|                                                                                                                           |
| We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all  |
| FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future            |
| activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided        |
| communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.                           |
|                                                                                                                           |
| - FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which    |
|   includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet        |
|   postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor      |
|   these correspondence/communication products as needed.                                                                  |
|                                                                                                                           |
| - Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders       |
|   understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities |
|   in implementing medical/special purpose device security.                                                                |
|                                                                                                                           |
| - FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements  |
|   for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance  |
|   to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; |
|   FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and         |
|   Vanguard Magazine.                                                                                                      |
|                                                                                                                           |
| - FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS        |
|   Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, |
|   support topic research, capture notes, poll the group on topics, and conduct live meetings.                             |
|                                                                                                                           |
| - FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security    |
|   matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and |
|   ad hoc training on new security initiatives.                                                                            |
+---------------------------------------------------------------------------------------------------------------------------+

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC                                                                                                                                                                     |
+================+================+================+================+================+================+================+================+================+================+
| Contracting Agency / Business                    | Department of Veterans Affairs                                                                                       |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Applicable Contract Number                       |                                                  | Total Dollar Value                               |                |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Contract Name                                    | Security Control Assessments                                                                                         |
+----------------+---------------------------------+---------------------------------+--------------------------------------------------+---------------------------------+
| Contract Type  | Firm Fixed Price (FFP)                                            | Place of Performance                             | Remote Locations                |
+----------------+----------------+---------------------------------+----------------+---------------------------------+----------------+---------------------------------+
| Prime or Subcontractor          |                                 | Period of Performance                            | 12 months from date of award, with four 12-month |
|                                 |                                 |                                                  | option periods.                                  |
+---------------------------------+---------------------------------+--------------------------------------------------+--------------------------------------------------+
| POC                             |                                                                                                                                       |
+---------------------------------+---------------------------------------------------------------------------------------------------------------------------------------+
| Background                                                                                                                                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and      |
| services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services  |
| to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and      |
| compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals.                                                |
|                                                                                                                                                                         |
| During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector       |
| General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls,              |
| configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously         |
| identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support        |
| Federal regulatory requirements and the Federal Government oversight and audit community.                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Scope of Work                                                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC performed:                                                                                                                                                          |
|                                                                                                                                                                         |
| 1.  Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented   |
|     correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting  |
|     security requirements.                                                                                                                                              |
|                                                                                                                                                                         |
| a)  Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the      |
|     test.                                                                                                                                                               |
|                                                                                                                                                                         |
| b)  Execute the SAP.                                                                                                                                                    |
|                                                                                                                                                                         |
| c)  Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).                           |
|                                                                                                                                                                         |
| 2.  Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within   |
|     or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall          |
|     effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with      |
|     respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the       |
|     information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above               |
|     responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.      |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                                                                 |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Contractor Independent Assessment                                                                                                                                       |
|                                                                                                                                                                         |
| ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information    |
| systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because  |
| SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an        |
| assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their         |
| environment of operation and recommend corrective actions to address identified vulnerabilities.                                                                        |
|                                                                                                                                                                         |
| ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization     |
| (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).   |
|                                                                                                                                                                         |
| ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed.                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment Preparation                                                                                                                                 |
|                                                                                                                                                                         |
| - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements |
|   captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to   |
|   develop a System Rules of Engagement (ROE) Agreement.                                                                                                                 |
|                                                                                                                                                                         |
| The ROE must correctly identify the following:                                                                                                                          |
|                                                                                                                                                                         |
| 1)  Scope of testing                                                                                                                                                    |
|                                                                                                                                                                         |
| a)  ABC only tested for controls within scope of system type, location, and within the appropriate management control                                                   |
|                                                                                                                                                                         |
| 2)  Network ranges being assessed                                                                                                                                       |
|                                                                                                                                                                         |
| 3)  System components being assessed                                                                                                                                    |
|                                                                                                                                                                         |
| 4)  Locations being assessed                                                                                                                                            |
|                                                                                                                                                                         |
| 5)  SCA and all members conducting assessments including systems being used                                                                                             |
|                                                                                                                                                                         |
| 6)  Assessment type and method                                                                                                                                          |
|                                                                                                                                                                         |
| 7)  Tools used for the assessment                                                                                                                                       |
|                                                                                                                                                                         |
| 8)  A detailed list of artifacts or evidence not of record with the system and needed to support the SCA                                                                |
|                                                                                                                                                                         |
| - ABC developed and submitted a SCA Test Plan which:                                                                                                                    |
|                                                                                                                                                                         |
| 1.  Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance    |
|     reviews), resources, and milestones for the system being tested.                                                                                                    |
|                                                                                                                                                                         |
| 2.  Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities.                         |
|                                                                                                                                                                         |
| 3.  Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA.                     |
|                                                                                                                                                                         |
| 4.  Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were   |
|     used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.                                                                   |
|                                                                                                                                                                         |
| 5.  Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all          |
|     necessary parties.                                                                                                                                                  |
|                                                                                                                                                                         |
| - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and       |
|   procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems       |
|   needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security      |
|   categorization.                                                                                                                                                       |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment                                                                                                                                             |
|                                                                                                                                                                         |
| - ABC performed the SCA according to the processes and procedures described in the SAP.                                                                                 |
|                                                                                                                                                                         |
| - ABC completed the following communication and reporting activities:                                                                                                   |
|                                                                                                                                                                         |
| 1.  SCA Pre-Site Meeting                                                                                                                                                |
|                                                                                                                                                                         |
| 2.  System Component Assessment Daily Status                                                                                                                            |
|                                                                                                                                                                         |
| 3.  Weekly Status Report                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  SCA Out-Brief Meeting                                                                                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Assessment Report                                                                                                                                              |
|                                                                                                                                                                         |
| - ABC developed the Security Assessment Report to include the following:                                                                                                |
|                                                                                                                                                                         |
| 1.  Documentation of each SCA                                                                                                                                           |
|                                                                                                                                                                         |
| 2.  Assessment test objectives as identified in NIST SP 800-53A                                                                                                         |
|                                                                                                                                                                         |
| 3.  Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A                                                                             |
|                                                                                                                                                                         |
| 4.  All software and hardware components assessed                                                                                                                       |
|                                                                                                                                                                         |
| 5.  Sequential, step-by-step assessment procedures for testing each test objective                                                                                      |
|                                                                                                                                                                         |
| 6.  Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment      |
|                                                                                                                                                                         |
| 7.  Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices        |
|                                                                                                                                                                         |
| 8.  Rationale for any system or device in the inventory not directly tested                                                                                             |
|                                                                                                                                                                         |
| 9.  Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed,     |
|     location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark                                         |
|                                                                                                                                                                         |
| 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or     |
|     “Other Than Satisfied” determination                                                                                                                                |
|                                                                                                                                                                         |
| 11. Results of Findings to be documented in the OCS POA&M Import Template (data                                                                                         |
|                                                                                                                                                                         |
| defined file in Excel format)                                                                                                                                           |
|                                                                                                                                                                         |
| 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual |
|     results for each system component type                                                                                                                              |
|                                                                                                                                                                         |
| 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO                                                         |
|                                                                                                                                                                         |
| - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and      |
|   reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)                              |
|                                                                                                                                                                         |
| - ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA |
|   Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned    |
|   were collected from the following individuals:                                                                                                                        |
|                                                                                                                                                                         |
| 1.  AO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 2.  SO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 3.  ISSO                                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  IT System Manager/System Steward                                                                                                                                    |
|                                                                                                                                                                         |
| 5.  Security Controls Assessor                                                                                                                                          |
|                                                                                                                                                                         |
| 6.  VA Program Manager                                                                                                                                                  |
|                                                                                                                                                                         |
| 7.  COR                                                                                                                                                                 |
|                                                                                                                                                                         |
| 8.  CO                                                                                                                                                                  |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Ongoing Security Control Assessments                                                                                                                                    |
|                                                                                                                                                                         |
| - ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks                           |
|                                                                                                                                                                         |
| - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.                                                             |
|                                                                                                                                                                         |
| - We performed all required communications and reporting activities as required by RMF Tasks                                                                            |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+



=================== Prompt for Section 1-7: Administrative and Compliance Responses ====================

Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** Administrative and Compliance Responses  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: [][A picture containing text, clipart Description automatically
generated][Logo Description automatically generated][A picture
containing shape Description automatically generated][][][][]

Volume I – Technical

“This proposal includes data that shall not be disclosed outside the
Government and shall not be duplicated, used or disclosed in whole or in
part for any purpose other than to evaluate this proposal. If, however,
a Contract is awarded to this Offeror as a result of or in connection
with the submission of this data, the Government shall have the right to
duplicate, use, or disclose the data to the extent provided in the
resulting Contract. This restriction does not limit the Government’s
right to use information contained in this data if it is obtained from
another source without restriction. The data subject to the restriction
is contained in all sheets of this proposal.”

Office of the Comptroller of the Currency (OCC)

Cyber Security Office (CSO)

Cybersecurity Assessment and Compliance (CA&C)

Solicitation Number: 2031JW22Q00022

Due Date: 10:00 AM (EST), October 29, 2021

October 29, 2021

Solicitation Number: 2031JW22Q00022

Agency: Office of the Comptroller of the Currency (OCC)

Subject: Team Greenbrier Response to Cybersecurity Assessment and
Compliance (CA&C)

Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter
referred to as “Team Greenbrier”) are pleased to submit this response to
the Office of the Comptroller of the Currency (OCC), Cybersecurity
Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion
for solving problems and ensuring the sustainability of results through
project leadership extends across multiple industries. Team Greenbrier’s
substantial past performance and reputation for excellence with our
clients attest to the fact that we continually deliver low-risk,
high-quality, value-added strategic support services.

Team Greenbrier has been providing Cybersecurity Assessment and
Compliance services for many years to a large base of clients in the
federal government and commercial space as part of projects that are
similar in size and scope to the work that will be part of the OCC
program. Our deep subject matter expertise in Cybersecurity Assessment,
Compliance, and experience in providing innovative services with proven
program management skills will foster the support needed to ensure the
best quality services.

We are enthusiastic about the opportunity to work with you and eager to
demonstrate immediately the positive impact we can have on your
organization. Team Greenbrier acknowledges receipt of the RFQ and
Amendments and takes no exception to the terms and conditions. Our
company information includes:

The information of teaming partner includes:

+---------------------------------------+------------------------------+
| - Company Name:                       | - DUNS Number:               |
+=======================================+==============================+

We are personally committed to the success of this program and agree
with all terms, conditions, & provisions and to furnish any or all items
included in the solicitation. With our dynamic leadership, supervision,
and more than capable personnel, we are confident that we are ideally
suited to fulfill your needs. If you have any questions or need
additional information, please contact our primary point of contact.

Respectfully,

[]

Scotty Johnson

President

Table of Contents

1 Assumptions 1

1.1 Technical Assumptions 1

1.2 Price Assumptions 1

2 SF 1449 1

3 Acknowledgment of Solicitation Amendments 1

4 Completion of Representations: 1

5 Attestation 1

6 Conflict of Interest Mitigation Plan 1

7 GSA Federal Supply Schedule (FSS) 1

8 Technical/Management Approach (No more than 15 pages) 2

8.1 Methodology/process for testing web applications, financial systems,
network and infrastructure devices and end-user devices, including a
detailed process for manual and automated testing 2

8.2 Methodology for assigning risk ratings to weaknesses discovered
during the assessment process 2

8.3 Process for audit preparation and tracking, distributing, and
responding to data call requests associated with annual external audits
(FISCAM, FISMA and A-123) 2

8.4 Process for integrating the Risk Management Framework (RMF) –
Monitor step with an eGRC tool 2

8.5 Process for automating assessments 2

8.6 Methodology for tracking, reporting and completing all work
identified in the PWS 2

8.7 Staffing plan and approach for obtaining and maintaining individuals
with the qualifications listed in the Key Personnel section 2

9 Key Personnel Resumes (No more than 3 pages per resume) 2

9.1 Project Manager 2

9.2 Cybersecurity Assurance & Compliance Program Support Lead 3

9.3 Security & Privacy Control Assessor Lead 3

9.4 Quality Assurance Lead 4

9.5 Tier 2 eGRC Specialist 4

9.6 Automated Assessment Developer 4

9.7 IT Security Subject Matter Expert 5

10 Past Performance 5

Assumptions

Technical Assumptions

<Insert Assumptions>

Price Assumptions

<Insert Assumptions>

SF 1449

Completed by the Quoter and signed by an authorized official of the
company.

<Insert SF 1449>

Acknowledgment of Solicitation Amendments

<Insert Response>

Completion of Representations:

OCC provision 1052.209-8001, Conflict of Interest Disclosure and
Certification (Feb 2014)

<Insert Reps and Certs>

Attestation

Attest by stating in its quote submission that “All quoted contractor
personnel meet Contractor Qualifications and Key Personnel outlined in
Section II, “Qualifications for Tasks/Key Personnel” in the SOW.

<Insert Response>

Conflict of Interest Mitigation Plan

Provide a description of any actual or potential conflicts of interest
related to this requirement. If the Quoter has identified any actual or
potential conflicts, the Quoter shall provide a mitigation plan. If
Quoter has identified no actual or potential conflicts of interest,
quoter shall provide a statement in writing to that affect (this can be
satisfied with completion of OCC Provision 1052.209-8001). Quoters may
submit a single OCI statement, however, this does not alleviate the
prime contractor’s responsibility to research potential conflicts of any
subcontractors and submit a mitigation strategy, if applicable.

<Insert Response>

GSA Federal Supply Schedule (FSS)

The vendor must explicitly confirm that all proposed services fall under
a vendor’s GSA Federal Supply Schedule (FSS). If the vendor has a
teaming arrangement with another vendor to provide any services in the
vendor’s quotation, please identify the vendor and the GSA FSS number
for those affected CLINs.

<Insert Response>

Technical/Management Approach (No more than 15 pages)

Contractors shall provide a technical/management approach that conveys
an understanding of the requirement and includes:

Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

<Insert Response>

Methodology for assigning risk ratings to weaknesses discovered during the assessment process

<Insert Response>

Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

<Insert Response>

Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

<Insert Response>

Process for automating assessments

<Insert Response>

Methodology for tracking, reporting and completing all work identified in the PWS

<Insert Response>

Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

<Insert Response>

Key Personnel Resumes (No more than 3 pages per resume)

The Quoter shall submit resumes based on the individual’s experience and
certifications, for each of the Key Personnel identified in the SOW. The
Quoter shall also provide a letter of commitment for each proposed key
personnel.

Project Manager

The Project Manager shall meet the following minimum criteria:

- At least eight (8) years of experience in program and project
  management supporting information security or cybersecurity projects
  for the federal government is required.

- A bachelor's degree from an accredited college and a Project
  Management Professional (PMP) or equivalent (as approved by the OCC
  COR) is required.

- Hands-on experience using the Microsoft Office Project is required.

- Experience with NIST Risk Management Framework and Governance, Risk &
  Compliance (GRC) and Information Assurance capabilities/tools (e.g.,
  ServiceNow GRC, RSA Archer, CSAM, Xacta, etc.) is required.

- Certified Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified Information Systems Security
  Professional (CISSP), or Certified Information Security Manager (CISM)
  is required.

<Insert Resume>

Cybersecurity Assurance & Compliance Program Support Lead

The Cybersecurity Assurance and Compliance Program Support Lead shall
meet the following minimum criteria, unless otherwise noted as
preferred:

- At least three (3) years of experience managing security compliance
  program support teams is required.

- At least six (6) years of experience developing RMF documentation in
  the last five years, including but not limited to: Standard Operating
  Procedures (SOPs), system security plans (SSPs), program plans,
  processes, workflows are required.

- At least eight (8) years of Information Security experience is
  required.

- At least two (2) years of experience using eGRC tools is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified in Risk and Information Systems Control
  (CRISC), or Certified Information Security Manager (CISM) is required.

- Experience with ServiceNow GRC CAM tool is preferred.

<Insert Resume>

Security & Privacy Control Assessor Lead

The Security and Privacy Control Assessor Lead shall meet the following
minimum criteria, unless otherwise noted as preferred:

- Three (3) years of experience managing security assessment teams is
  required.

- Six (6) years of experience performing detailed, full-scope technical
  security control testing for each of the component types listed in
  Section C.3.1, including development of security and privacy
  assessment plans is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with the use of eGRC tools is required.
  Experience with ServiceNow is preferred.

- Experience working with Qualys Enterprise, Archer, Nessus, HCL
  AppScan, and technical outputs of the Kali Linux suite is required.

- Advanced knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Ethical Hacker (CEH), Certified Risk and Information Systems Control
  (CRISC), or Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Quality Assurance Lead

The Quality Assurance (QA) Lead shall meet the following minimum
criteria, unless otherwise noted as preferred:

- Three (3) years of managing technical security QA team/SA&A Package
  Independent Validation & Verification (IV&V) is required.

- Six (6) years of experience developing RMF documentation is required.

- Six (6) years of experience conducting security and privacy control
  assessments is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with eGRC tools is required. Experience
  with ServiceNow GRC tool suite, including CAM, is preferred.

- Certified in Certified Information Systems Security Professional
  (CISSP), Certified Risk and Information Systems Control (CRISC), or
  Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Tier 2 eGRC Specialist

The Tier 2 eGRC Specialist shall meet the following minimum criteria,
unless otherwise noted as preferred:

- At least two (2) years of experience with developing/writing advanced
  custom ServiceNow script includes, user interface actions, user
  interface policies, access control lists, client scripts, scheduled
  jobs, data tables and data fields in the last three years is required.

- At least three (3) years of Integrated Risk Management (IRM)/GRC
  implementation experience in the last four years is required; GRC CAM
  experience is preferred. ServiceNow Certified Risk & Compliance
  implementation specialist is preferred.

- At least (2) years of experience configuring and customizing the ITSM
  suite, IT Operations Management suite, and NOW Platform Capabilities
  in the last three years is required.

- At least (2) years of experience with technical components such as
  LDAP, Web Services, REST, SOAP, APIs, XML, JavaScript in the last
  three years is required.

- At least (2) years of experience with ITILv3 Service Management
  processes in the last three years is required.

- At least three (3) years of Administrator/Developer experience in the
  last four years is required; in addition, GRC ServiceNow Certified
  Developer is preferred.

- At least two (2) years of ServiceNow Implementation Specialist
  experience in the last three years is required; in addition, Certified
  ServiceNow Implementation Specialist is preferred.

<Insert Resume>

Automated Assessment Developer

The Automated Assessment Developer shall meet the following minimum
criteria:

- Five (5) years of experience as a software developer is required,

- Three (3) years of experience integrating cybersecurity tools,
  including Qualys, RSA Archer, and Splunk with ServiceNow GRC and
  ServiceNow formats and languages listed in Task C.2.7 is preferred.

- Bachelor of Science degree in Computer Science is required.

<Insert Resume>

IT Security Subject Matter Expert

The IT Security Subject-Matter Expert (SME), who handles areas requiring
elevated technical security skills (e.g., risk analysis of cutting-edge
technology, or high visibility project that requires a SME review due to
quick turn around with accurate results, etc.), and technical escalation
concerns that may arise in the implementation of new IT security and/or
compliance program requirements, security assessments, technical SA&A
package evaluations, etc., shall meet the following minimum criteria,
unless otherwise noted as preferred:

- Ten (10) years of IT security experience is required.

- Five (5) years of experience performing detailed, full-scope technical
  control testing for the component types listed in Section C.3.1 of
  this SOW including development security assessment plans is required.

- In-depth knowledge of and experience in implementing and using GRC
  platforms is required. Experience with ServiceNow GRC tool suite,
  including CAM preferred.

- Hands-on experience working with at least four (4) of the following
  seven (7) technology products: Forescout CounterAct, ArcSight, HCL
  BigFix, Sailpoint, CyberArk, RES, and Splunk is required.

- Extensive knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Bachelor of Science degree in Computer Science, Cybersecurity, or
  Information Systems. is required.

- Certified Information Systems Security Professional (CISSP) is
  required.

- Certified Cloud Security Professional (CCSP) is preferred.

<Insert Resume>

Past Performance

The Quoter shall request that references fill out and return three (3)
Past Performance Questionnaire Survey (Attachment 3). Quoters may not
submit the completed survey forms to the OCC Contract Specialist on
behalf of their references; instead, the completed forms shall be
returned directly by the referenced customers to the OCC Contract
Specialist, per the information provided on the form. The forms are due
to the OCC no later than the specified deadline for submitting
quotations in response to this RFQ. The OCC may also obtain and consider
any other relevant information in evaluating past performance, including
information contained in Contractor Performance Assessment Reporting
System (CPAR).
  
- Section Outline: Administrative and Compliance Responses  
- RFP Summary: 
Proposal Info
•  Solicitation Number: RFQ 2031JW22Q00022 [Page 17]
•  NAICS Code: 541519 [Page 51]
•  Name/Title: Cybersecurity Assessment and Compliance (CA&C) Support [Page 1]
•  Solicitation Type: RFQ [Page 17]
•  Department/Agency Name: Comptroller of the Currency [Page 1]
•  Inquiries/Questions Due Date and Time: 12:00 PM (EST), October 12, 2021 [Page
51]
•  Proposal Due Date and Time: 10:00 AM (EST), October 29, 2021 [Page 50]
•  Mode of Submission: eBuy [Page 50]
•  Place of Performance: Online [Page 50]
•  Point of Contact (POC):
○  Primary: <EMAIL> [Page 51]
○  Secondary: <EMAIL> [Page 51]
•  Set Aside: Small Business [Page 1]
•  Period of Performance:
○  Base Period: 12 months, 01/03/2022 to 01/02/2023 [Page 2]
○  Option Period 1: 12 months, 01/03/2023 to 01/02/2024 [Page 3]
○  Option Period 2: 12 months, 01/03/2024 to 01/02/2025 [Page 4]
○  Option Period 3: 12 months, 01/03/2025 to 01/02/2026 [Page 5]
○  Option Period 4: 12 months, 01/03/2026 to 01/02/2027 [Page 5]
•  Key Personnel:
○  Project Manager [Page 22]
○  Cybersecurity Assurance and Compliance Program Support Lead [Page 22]
○  Security and Privacy Control Assessor Lead [Page 23]
○  Quality Assurance Lead [Page 24]
○  Tier 2 eGRC Specialist [Page 23]
○  Automated Assessment Developer [Page 23]
○  IT Security Subject Matter Expert [Page 23]
•  Security Clearance Requirements: N/A [Page 49]
•  Task Order Type: N/A [Page 50]
Purpose
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). [Section II - Statement of Work (SOW)] The CA&C
Program includes agency-level oversight for compliance with the Risk Management
Framework (RMF), Audit Liaison, and execution of Assessment and Authorization steps of the
RMF. The scope of work shall comprise: Project Management and Reporting; Cyber
Assurance and Compliance (CA&C) Program Support, including, Audit Liaison, Compliance
Oversight activities, CA&C program security documentation, and technical support for
automation and program tools; Security & Privacy Control Assessments of new and existing
OCC systems; Quality Assurance; and Administrative and IT Security Subject Matter Expert
Support. [Section II - Statement of Work (SOW)]
Scope
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). The CA&C Program includes agency-level oversight
for compliance with the Risk Management Framework (RMF), Audit Liaison, and execution of
Assessment and Authorization steps of the RMF. The scope of work shall comprise: Project
Management and Reporting; Cyber Assurance and Compliance (CA&C) Program Support,
including, Audit Liaison, Compliance Oversight activities, CA&C program security
documentation, and technical support for automation and program tools; Security & Privacy
Control Assessments of new and existing OCC systems; Quality Assurance; and
Administrative and IT Security Subject Matter Expert Support.
Task Areas Overview
•  C.1 Project Management and Reporting
•  C.2 Cyber Assurance and Compliance Program Support
•  C.3 Security and Privacy Control Assessments
•  C.4 Quality Assurance
•  C.5 Administrative Support
•  C.6 IT Security Subject Matter Expert Support

  
- Knowledge Base: +---------------------------------------------------------------------------------------------------------------------------+
| ABC Government Solutions, Inc                                                                                             |
+==================+==============+==============+==============+==============+==============+==============+==============+
| Contracting Agency / Business   | Department of Veterans Affairs                                                          |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Applicable Contract Number      |                             | Total Dollar Value          |                             |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Contract Name                   | FSS Program Management Support (HMS Technologies)                                       |
+---------------------------------+-----------------------------+--------------------------------------------+--------------+
| Contract Type                   | FFP                         | Place of Performance                       | Remote       |
+------------------+--------------+--------------+--------------+--------------+-----------------------------+--------------+
| Prime or         | Subcontractor               | Period of Performance       |                                            |
| Subcontractor    |                             |                             |                                            |
+------------------+-----------------------------+-----------------------------+--------------------------------------------+
| POC              |                                                                                                        |
+------------------+--------------------------------------------------------------------------------------------------------+
| Description of Services                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in      |
|   order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS             |
|   requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness       |
|   Information Security Protection (CRISP) project.                                                                        |
|                                                                                                                           |
| - We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones  |
|   and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities      |
|   associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones    |
|   (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track     |
|   progress status of VA wide tasks.                                                                                       |
+---------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - FSS SHAREPOINT SUPPORT                                                                                                  |
|                                                                                                                           |
| ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for             |
| improvements in FSS business operations methods to achieve improvements in FSS business operations which included:        |
|                                                                                                                           |
| 1.  Business process or procedures changes                                                                                |
|                                                                                                                           |
| 2.  Application of pertinent Technology (e.g., SharePoint tracking)                                                       |
|                                                                                                                           |
| 3.  Ability to utilize SharePoint applications to improve business operations                                             |
|                                                                                                                           |
| We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation)   |
| that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As   |
| part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with   |
| links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other       |
| similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.                     |
|                                                                                                                           |
| - FSS CRISP PROGRAM SUPPORT                                                                                               |
|                                                                                                                           |
| ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components,     |
| milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of  |
| CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.   |
|                                                                                                                           |
| We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts      |
| (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with |
| CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, |
| action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress         |
| upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided       |
| weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications    |
| System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues,   |
| risks and accomplishments associated with FSS CRISP sustainment.                                                          |
|                                                                                                                           |
| We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS    |
| Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks,    |
| and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS         |
| Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues     |
| associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All  |
| weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any     |
| distribution.                                                                                                             |
|                                                                                                                           |
| - GOVERNANCE SUPPORT                                                                                                      |
|                                                                                                                           |
| ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to      |
| include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and    |
| workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments,  |
| delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and       |
| potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which    |
| measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics |
| were maintained and continuously revised, by us, as the need for change arises. We provided project governance management |
| expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported   |
| the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization,    |
| and present it to FSS leadership for review and approval.                                                                 |
|                                                                                                                           |
| As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and     |
| facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned   |
| with FSS mission and goals.                                                                                               |
|                                                                                                                           |
| - FSS REQUIREMENTS ELABORATION PLANNING SUPPORT                                                                           |
|                                                                                                                           |
| As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements    |
| Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan     |
| includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and    |
| OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS     |
| Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in   |
| the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and      |
| channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and          |
| communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and      |
| approval from the COR/FSS VA PM.                                                                                          |
|                                                                                                                           |
| We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all  |
| FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future            |
| activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided        |
| communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.                           |
|                                                                                                                           |
| - FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which    |
|   includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet        |
|   postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor      |
|   these correspondence/communication products as needed.                                                                  |
|                                                                                                                           |
| - Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders       |
|   understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities |
|   in implementing medical/special purpose device security.                                                                |
|                                                                                                                           |
| - FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements  |
|   for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance  |
|   to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; |
|   FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and         |
|   Vanguard Magazine.                                                                                                      |
|                                                                                                                           |
| - FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS        |
|   Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, |
|   support topic research, capture notes, poll the group on topics, and conduct live meetings.                             |
|                                                                                                                           |
| - FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security    |
|   matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and |
|   ad hoc training on new security initiatives.                                                                            |
+---------------------------------------------------------------------------------------------------------------------------+

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC                                                                                                                                                                     |
+================+================+================+================+================+================+================+================+================+================+
| Contracting Agency / Business                    | Department of Veterans Affairs                                                                                       |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Applicable Contract Number                       |                                                  | Total Dollar Value                               |                |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Contract Name                                    | Security Control Assessments                                                                                         |
+----------------+---------------------------------+---------------------------------+--------------------------------------------------+---------------------------------+
| Contract Type  | Firm Fixed Price (FFP)                                            | Place of Performance                             | Remote Locations                |
+----------------+----------------+---------------------------------+----------------+---------------------------------+----------------+---------------------------------+
| Prime or Subcontractor          |                                 | Period of Performance                            | 12 months from date of award, with four 12-month |
|                                 |                                 |                                                  | option periods.                                  |
+---------------------------------+---------------------------------+--------------------------------------------------+--------------------------------------------------+
| POC                             |                                                                                                                                       |
+---------------------------------+---------------------------------------------------------------------------------------------------------------------------------------+
| Background                                                                                                                                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and      |
| services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services  |
| to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and      |
| compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals.                                                |
|                                                                                                                                                                         |
| During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector       |
| General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls,              |
| configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously         |
| identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support        |
| Federal regulatory requirements and the Federal Government oversight and audit community.                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Scope of Work                                                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC performed:                                                                                                                                                          |
|                                                                                                                                                                         |
| 1.  Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented   |
|     correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting  |
|     security requirements.                                                                                                                                              |
|                                                                                                                                                                         |
| a)  Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the      |
|     test.                                                                                                                                                               |
|                                                                                                                                                                         |
| b)  Execute the SAP.                                                                                                                                                    |
|                                                                                                                                                                         |
| c)  Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).                           |
|                                                                                                                                                                         |
| 2.  Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within   |
|     or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall          |
|     effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with      |
|     respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the       |
|     information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above               |
|     responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.      |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                                                                 |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Contractor Independent Assessment                                                                                                                                       |
|                                                                                                                                                                         |
| ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information    |
| systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because  |
| SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an        |
| assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their         |
| environment of operation and recommend corrective actions to address identified vulnerabilities.                                                                        |
|                                                                                                                                                                         |
| ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization     |
| (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).   |
|                                                                                                                                                                         |
| ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed.                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment Preparation                                                                                                                                 |
|                                                                                                                                                                         |
| - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements |
|   captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to   |
|   develop a System Rules of Engagement (ROE) Agreement.                                                                                                                 |
|                                                                                                                                                                         |
| The ROE must correctly identify the following:                                                                                                                          |
|                                                                                                                                                                         |
| 1)  Scope of testing                                                                                                                                                    |
|                                                                                                                                                                         |
| a)  ABC only tested for controls within scope of system type, location, and within the appropriate management control                                                   |
|                                                                                                                                                                         |
| 2)  Network ranges being assessed                                                                                                                                       |
|                                                                                                                                                                         |
| 3)  System components being assessed                                                                                                                                    |
|                                                                                                                                                                         |
| 4)  Locations being assessed                                                                                                                                            |
|                                                                                                                                                                         |
| 5)  SCA and all members conducting assessments including systems being used                                                                                             |
|                                                                                                                                                                         |
| 6)  Assessment type and method                                                                                                                                          |
|                                                                                                                                                                         |
| 7)  Tools used for the assessment                                                                                                                                       |
|                                                                                                                                                                         |
| 8)  A detailed list of artifacts or evidence not of record with the system and needed to support the SCA                                                                |
|                                                                                                                                                                         |
| - ABC developed and submitted a SCA Test Plan which:                                                                                                                    |
|                                                                                                                                                                         |
| 1.  Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance    |
|     reviews), resources, and milestones for the system being tested.                                                                                                    |
|                                                                                                                                                                         |
| 2.  Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities.                         |
|                                                                                                                                                                         |
| 3.  Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA.                     |
|                                                                                                                                                                         |
| 4.  Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were   |
|     used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.                                                                   |
|                                                                                                                                                                         |
| 5.  Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all          |
|     necessary parties.                                                                                                                                                  |
|                                                                                                                                                                         |
| - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and       |
|   procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems       |
|   needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security      |
|   categorization.                                                                                                                                                       |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment                                                                                                                                             |
|                                                                                                                                                                         |
| - ABC performed the SCA according to the processes and procedures described in the SAP.                                                                                 |
|                                                                                                                                                                         |
| - ABC completed the following communication and reporting activities:                                                                                                   |
|                                                                                                                                                                         |
| 1.  SCA Pre-Site Meeting                                                                                                                                                |
|                                                                                                                                                                         |
| 2.  System Component Assessment Daily Status                                                                                                                            |
|                                                                                                                                                                         |
| 3.  Weekly Status Report                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  SCA Out-Brief Meeting                                                                                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Assessment Report                                                                                                                                              |
|                                                                                                                                                                         |
| - ABC developed the Security Assessment Report to include the following:                                                                                                |
|                                                                                                                                                                         |
| 1.  Documentation of each SCA                                                                                                                                           |
|                                                                                                                                                                         |
| 2.  Assessment test objectives as identified in NIST SP 800-53A                                                                                                         |
|                                                                                                                                                                         |
| 3.  Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A                                                                             |
|                                                                                                                                                                         |
| 4.  All software and hardware components assessed                                                                                                                       |
|                                                                                                                                                                         |
| 5.  Sequential, step-by-step assessment procedures for testing each test objective                                                                                      |
|                                                                                                                                                                         |
| 6.  Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment      |
|                                                                                                                                                                         |
| 7.  Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices        |
|                                                                                                                                                                         |
| 8.  Rationale for any system or device in the inventory not directly tested                                                                                             |
|                                                                                                                                                                         |
| 9.  Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed,     |
|     location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark                                         |
|                                                                                                                                                                         |
| 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or     |
|     “Other Than Satisfied” determination                                                                                                                                |
|                                                                                                                                                                         |
| 11. Results of Findings to be documented in the OCS POA&M Import Template (data                                                                                         |
|                                                                                                                                                                         |
| defined file in Excel format)                                                                                                                                           |
|                                                                                                                                                                         |
| 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual |
|     results for each system component type                                                                                                                              |
|                                                                                                                                                                         |
| 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO                                                         |
|                                                                                                                                                                         |
| - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and      |
|   reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)                              |
|                                                                                                                                                                         |
| - ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA |
|   Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned    |
|   were collected from the following individuals:                                                                                                                        |
|                                                                                                                                                                         |
| 1.  AO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 2.  SO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 3.  ISSO                                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  IT System Manager/System Steward                                                                                                                                    |
|                                                                                                                                                                         |
| 5.  Security Controls Assessor                                                                                                                                          |
|                                                                                                                                                                         |
| 6.  VA Program Manager                                                                                                                                                  |
|                                                                                                                                                                         |
| 7.  COR                                                                                                                                                                 |
|                                                                                                                                                                         |
| 8.  CO                                                                                                                                                                  |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Ongoing Security Control Assessments                                                                                                                                    |
|                                                                                                                                                                         |
| - ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks                           |
|                                                                                                                                                                         |
| - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.                                                             |
|                                                                                                                                                                         |
| - We performed all required communications and reporting activities as required by RMF Tasks                                                                            |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+



=================== Prompt for Section 8: Technical/Management Approach ====================

Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** Technical/Management Approach  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: [][A picture containing text, clipart Description automatically
generated][Logo Description automatically generated][A picture
containing shape Description automatically generated][][][][]

Volume I – Technical

“This proposal includes data that shall not be disclosed outside the
Government and shall not be duplicated, used or disclosed in whole or in
part for any purpose other than to evaluate this proposal. If, however,
a Contract is awarded to this Offeror as a result of or in connection
with the submission of this data, the Government shall have the right to
duplicate, use, or disclose the data to the extent provided in the
resulting Contract. This restriction does not limit the Government’s
right to use information contained in this data if it is obtained from
another source without restriction. The data subject to the restriction
is contained in all sheets of this proposal.”

Office of the Comptroller of the Currency (OCC)

Cyber Security Office (CSO)

Cybersecurity Assessment and Compliance (CA&C)

Solicitation Number: 2031JW22Q00022

Due Date: 10:00 AM (EST), October 29, 2021

October 29, 2021

Solicitation Number: 2031JW22Q00022

Agency: Office of the Comptroller of the Currency (OCC)

Subject: Team Greenbrier Response to Cybersecurity Assessment and
Compliance (CA&C)

Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter
referred to as “Team Greenbrier”) are pleased to submit this response to
the Office of the Comptroller of the Currency (OCC), Cybersecurity
Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion
for solving problems and ensuring the sustainability of results through
project leadership extends across multiple industries. Team Greenbrier’s
substantial past performance and reputation for excellence with our
clients attest to the fact that we continually deliver low-risk,
high-quality, value-added strategic support services.

Team Greenbrier has been providing Cybersecurity Assessment and
Compliance services for many years to a large base of clients in the
federal government and commercial space as part of projects that are
similar in size and scope to the work that will be part of the OCC
program. Our deep subject matter expertise in Cybersecurity Assessment,
Compliance, and experience in providing innovative services with proven
program management skills will foster the support needed to ensure the
best quality services.

We are enthusiastic about the opportunity to work with you and eager to
demonstrate immediately the positive impact we can have on your
organization. Team Greenbrier acknowledges receipt of the RFQ and
Amendments and takes no exception to the terms and conditions. Our
company information includes:

The information of teaming partner includes:

+---------------------------------------+------------------------------+
| - Company Name:                       | - DUNS Number:               |
+=======================================+==============================+

We are personally committed to the success of this program and agree
with all terms, conditions, & provisions and to furnish any or all items
included in the solicitation. With our dynamic leadership, supervision,
and more than capable personnel, we are confident that we are ideally
suited to fulfill your needs. If you have any questions or need
additional information, please contact our primary point of contact.

Respectfully,

[]

Scotty Johnson

President

Table of Contents

1 Assumptions 1

1.1 Technical Assumptions 1

1.2 Price Assumptions 1

2 SF 1449 1

3 Acknowledgment of Solicitation Amendments 1

4 Completion of Representations: 1

5 Attestation 1

6 Conflict of Interest Mitigation Plan 1

7 GSA Federal Supply Schedule (FSS) 1

8 Technical/Management Approach (No more than 15 pages) 2

8.1 Methodology/process for testing web applications, financial systems,
network and infrastructure devices and end-user devices, including a
detailed process for manual and automated testing 2

8.2 Methodology for assigning risk ratings to weaknesses discovered
during the assessment process 2

8.3 Process for audit preparation and tracking, distributing, and
responding to data call requests associated with annual external audits
(FISCAM, FISMA and A-123) 2

8.4 Process for integrating the Risk Management Framework (RMF) –
Monitor step with an eGRC tool 2

8.5 Process for automating assessments 2

8.6 Methodology for tracking, reporting and completing all work
identified in the PWS 2

8.7 Staffing plan and approach for obtaining and maintaining individuals
with the qualifications listed in the Key Personnel section 2

9 Key Personnel Resumes (No more than 3 pages per resume) 2

9.1 Project Manager 2

9.2 Cybersecurity Assurance & Compliance Program Support Lead 3

9.3 Security & Privacy Control Assessor Lead 3

9.4 Quality Assurance Lead 4

9.5 Tier 2 eGRC Specialist 4

9.6 Automated Assessment Developer 4

9.7 IT Security Subject Matter Expert 5

10 Past Performance 5

Assumptions

Technical Assumptions

<Insert Assumptions>

Price Assumptions

<Insert Assumptions>

SF 1449

Completed by the Quoter and signed by an authorized official of the
company.

<Insert SF 1449>

Acknowledgment of Solicitation Amendments

<Insert Response>

Completion of Representations:

OCC provision 1052.209-8001, Conflict of Interest Disclosure and
Certification (Feb 2014)

<Insert Reps and Certs>

Attestation

Attest by stating in its quote submission that “All quoted contractor
personnel meet Contractor Qualifications and Key Personnel outlined in
Section II, “Qualifications for Tasks/Key Personnel” in the SOW.

<Insert Response>

Conflict of Interest Mitigation Plan

Provide a description of any actual or potential conflicts of interest
related to this requirement. If the Quoter has identified any actual or
potential conflicts, the Quoter shall provide a mitigation plan. If
Quoter has identified no actual or potential conflicts of interest,
quoter shall provide a statement in writing to that affect (this can be
satisfied with completion of OCC Provision 1052.209-8001). Quoters may
submit a single OCI statement, however, this does not alleviate the
prime contractor’s responsibility to research potential conflicts of any
subcontractors and submit a mitigation strategy, if applicable.

<Insert Response>

GSA Federal Supply Schedule (FSS)

The vendor must explicitly confirm that all proposed services fall under
a vendor’s GSA Federal Supply Schedule (FSS). If the vendor has a
teaming arrangement with another vendor to provide any services in the
vendor’s quotation, please identify the vendor and the GSA FSS number
for those affected CLINs.

<Insert Response>

Technical/Management Approach (No more than 15 pages)

Contractors shall provide a technical/management approach that conveys
an understanding of the requirement and includes:

Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

<Insert Response>

Methodology for assigning risk ratings to weaknesses discovered during the assessment process

<Insert Response>

Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

<Insert Response>

Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

<Insert Response>

Process for automating assessments

<Insert Response>

Methodology for tracking, reporting and completing all work identified in the PWS

<Insert Response>

Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

<Insert Response>

Key Personnel Resumes (No more than 3 pages per resume)

The Quoter shall submit resumes based on the individual’s experience and
certifications, for each of the Key Personnel identified in the SOW. The
Quoter shall also provide a letter of commitment for each proposed key
personnel.

Project Manager

The Project Manager shall meet the following minimum criteria:

- At least eight (8) years of experience in program and project
  management supporting information security or cybersecurity projects
  for the federal government is required.

- A bachelor's degree from an accredited college and a Project
  Management Professional (PMP) or equivalent (as approved by the OCC
  COR) is required.

- Hands-on experience using the Microsoft Office Project is required.

- Experience with NIST Risk Management Framework and Governance, Risk &
  Compliance (GRC) and Information Assurance capabilities/tools (e.g.,
  ServiceNow GRC, RSA Archer, CSAM, Xacta, etc.) is required.

- Certified Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified Information Systems Security
  Professional (CISSP), or Certified Information Security Manager (CISM)
  is required.

<Insert Resume>

Cybersecurity Assurance & Compliance Program Support Lead

The Cybersecurity Assurance and Compliance Program Support Lead shall
meet the following minimum criteria, unless otherwise noted as
preferred:

- At least three (3) years of experience managing security compliance
  program support teams is required.

- At least six (6) years of experience developing RMF documentation in
  the last five years, including but not limited to: Standard Operating
  Procedures (SOPs), system security plans (SSPs), program plans,
  processes, workflows are required.

- At least eight (8) years of Information Security experience is
  required.

- At least two (2) years of experience using eGRC tools is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified in Risk and Information Systems Control
  (CRISC), or Certified Information Security Manager (CISM) is required.

- Experience with ServiceNow GRC CAM tool is preferred.

<Insert Resume>

Security & Privacy Control Assessor Lead

The Security and Privacy Control Assessor Lead shall meet the following
minimum criteria, unless otherwise noted as preferred:

- Three (3) years of experience managing security assessment teams is
  required.

- Six (6) years of experience performing detailed, full-scope technical
  security control testing for each of the component types listed in
  Section C.3.1, including development of security and privacy
  assessment plans is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with the use of eGRC tools is required.
  Experience with ServiceNow is preferred.

- Experience working with Qualys Enterprise, Archer, Nessus, HCL
  AppScan, and technical outputs of the Kali Linux suite is required.

- Advanced knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Ethical Hacker (CEH), Certified Risk and Information Systems Control
  (CRISC), or Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Quality Assurance Lead

The Quality Assurance (QA) Lead shall meet the following minimum
criteria, unless otherwise noted as preferred:

- Three (3) years of managing technical security QA team/SA&A Package
  Independent Validation & Verification (IV&V) is required.

- Six (6) years of experience developing RMF documentation is required.

- Six (6) years of experience conducting security and privacy control
  assessments is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with eGRC tools is required. Experience
  with ServiceNow GRC tool suite, including CAM, is preferred.

- Certified in Certified Information Systems Security Professional
  (CISSP), Certified Risk and Information Systems Control (CRISC), or
  Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Tier 2 eGRC Specialist

The Tier 2 eGRC Specialist shall meet the following minimum criteria,
unless otherwise noted as preferred:

- At least two (2) years of experience with developing/writing advanced
  custom ServiceNow script includes, user interface actions, user
  interface policies, access control lists, client scripts, scheduled
  jobs, data tables and data fields in the last three years is required.

- At least three (3) years of Integrated Risk Management (IRM)/GRC
  implementation experience in the last four years is required; GRC CAM
  experience is preferred. ServiceNow Certified Risk & Compliance
  implementation specialist is preferred.

- At least (2) years of experience configuring and customizing the ITSM
  suite, IT Operations Management suite, and NOW Platform Capabilities
  in the last three years is required.

- At least (2) years of experience with technical components such as
  LDAP, Web Services, REST, SOAP, APIs, XML, JavaScript in the last
  three years is required.

- At least (2) years of experience with ITILv3 Service Management
  processes in the last three years is required.

- At least three (3) years of Administrator/Developer experience in the
  last four years is required; in addition, GRC ServiceNow Certified
  Developer is preferred.

- At least two (2) years of ServiceNow Implementation Specialist
  experience in the last three years is required; in addition, Certified
  ServiceNow Implementation Specialist is preferred.

<Insert Resume>

Automated Assessment Developer

The Automated Assessment Developer shall meet the following minimum
criteria:

- Five (5) years of experience as a software developer is required,

- Three (3) years of experience integrating cybersecurity tools,
  including Qualys, RSA Archer, and Splunk with ServiceNow GRC and
  ServiceNow formats and languages listed in Task C.2.7 is preferred.

- Bachelor of Science degree in Computer Science is required.

<Insert Resume>

IT Security Subject Matter Expert

The IT Security Subject-Matter Expert (SME), who handles areas requiring
elevated technical security skills (e.g., risk analysis of cutting-edge
technology, or high visibility project that requires a SME review due to
quick turn around with accurate results, etc.), and technical escalation
concerns that may arise in the implementation of new IT security and/or
compliance program requirements, security assessments, technical SA&A
package evaluations, etc., shall meet the following minimum criteria,
unless otherwise noted as preferred:

- Ten (10) years of IT security experience is required.

- Five (5) years of experience performing detailed, full-scope technical
  control testing for the component types listed in Section C.3.1 of
  this SOW including development security assessment plans is required.

- In-depth knowledge of and experience in implementing and using GRC
  platforms is required. Experience with ServiceNow GRC tool suite,
  including CAM preferred.

- Hands-on experience working with at least four (4) of the following
  seven (7) technology products: Forescout CounterAct, ArcSight, HCL
  BigFix, Sailpoint, CyberArk, RES, and Splunk is required.

- Extensive knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Bachelor of Science degree in Computer Science, Cybersecurity, or
  Information Systems. is required.

- Certified Information Systems Security Professional (CISSP) is
  required.

- Certified Cloud Security Professional (CCSP) is preferred.

<Insert Resume>

Past Performance

The Quoter shall request that references fill out and return three (3)
Past Performance Questionnaire Survey (Attachment 3). Quoters may not
submit the completed survey forms to the OCC Contract Specialist on
behalf of their references; instead, the completed forms shall be
returned directly by the referenced customers to the OCC Contract
Specialist, per the information provided on the form. The forms are due
to the OCC no later than the specified deadline for submitting
quotations in response to this RFQ. The OCC may also obtain and consider
any other relevant information in evaluating past performance, including
information contained in Contractor Performance Assessment Reporting
System (CPAR).
  
- Section Outline: Technical/Management Approach  
- RFP Summary: 
Proposal Info
•  Solicitation Number: RFQ 2031JW22Q00022 [Page 17]
•  NAICS Code: 541519 [Page 51]
•  Name/Title: Cybersecurity Assessment and Compliance (CA&C) Support [Page 1]
•  Solicitation Type: RFQ [Page 17]
•  Department/Agency Name: Comptroller of the Currency [Page 1]
•  Inquiries/Questions Due Date and Time: 12:00 PM (EST), October 12, 2021 [Page
51]
•  Proposal Due Date and Time: 10:00 AM (EST), October 29, 2021 [Page 50]
•  Mode of Submission: eBuy [Page 50]
•  Place of Performance: Online [Page 50]
•  Point of Contact (POC):
○  Primary: <EMAIL> [Page 51]
○  Secondary: <EMAIL> [Page 51]
•  Set Aside: Small Business [Page 1]
•  Period of Performance:
○  Base Period: 12 months, 01/03/2022 to 01/02/2023 [Page 2]
○  Option Period 1: 12 months, 01/03/2023 to 01/02/2024 [Page 3]
○  Option Period 2: 12 months, 01/03/2024 to 01/02/2025 [Page 4]
○  Option Period 3: 12 months, 01/03/2025 to 01/02/2026 [Page 5]
○  Option Period 4: 12 months, 01/03/2026 to 01/02/2027 [Page 5]
•  Key Personnel:
○  Project Manager [Page 22]
○  Cybersecurity Assurance and Compliance Program Support Lead [Page 22]
○  Security and Privacy Control Assessor Lead [Page 23]
○  Quality Assurance Lead [Page 24]
○  Tier 2 eGRC Specialist [Page 23]
○  Automated Assessment Developer [Page 23]
○  IT Security Subject Matter Expert [Page 23]
•  Security Clearance Requirements: N/A [Page 49]
•  Task Order Type: N/A [Page 50]
Purpose
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). [Section II - Statement of Work (SOW)] The CA&C
Program includes agency-level oversight for compliance with the Risk Management
Framework (RMF), Audit Liaison, and execution of Assessment and Authorization steps of the
RMF. The scope of work shall comprise: Project Management and Reporting; Cyber
Assurance and Compliance (CA&C) Program Support, including, Audit Liaison, Compliance
Oversight activities, CA&C program security documentation, and technical support for
automation and program tools; Security & Privacy Control Assessments of new and existing
OCC systems; Quality Assurance; and Administrative and IT Security Subject Matter Expert
Support. [Section II - Statement of Work (SOW)]
Scope
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). The CA&C Program includes agency-level oversight
for compliance with the Risk Management Framework (RMF), Audit Liaison, and execution of
Assessment and Authorization steps of the RMF. The scope of work shall comprise: Project
Management and Reporting; Cyber Assurance and Compliance (CA&C) Program Support,
including, Audit Liaison, Compliance Oversight activities, CA&C program security
documentation, and technical support for automation and program tools; Security & Privacy
Control Assessments of new and existing OCC systems; Quality Assurance; and
Administrative and IT Security Subject Matter Expert Support.
Task Areas Overview
•  C.1 Project Management and Reporting
•  C.2 Cyber Assurance and Compliance Program Support
•  C.3 Security and Privacy Control Assessments
•  C.4 Quality Assurance
•  C.5 Administrative Support
•  C.6 IT Security Subject Matter Expert Support

  
- Knowledge Base: +---------------------------------------------------------------------------------------------------------------------------+
| ABC Government Solutions, Inc                                                                                             |
+==================+==============+==============+==============+==============+==============+==============+==============+
| Contracting Agency / Business   | Department of Veterans Affairs                                                          |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Applicable Contract Number      |                             | Total Dollar Value          |                             |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Contract Name                   | FSS Program Management Support (HMS Technologies)                                       |
+---------------------------------+-----------------------------+--------------------------------------------+--------------+
| Contract Type                   | FFP                         | Place of Performance                       | Remote       |
+------------------+--------------+--------------+--------------+--------------+-----------------------------+--------------+
| Prime or         | Subcontractor               | Period of Performance       |                                            |
| Subcontractor    |                             |                             |                                            |
+------------------+-----------------------------+-----------------------------+--------------------------------------------+
| POC              |                                                                                                        |
+------------------+--------------------------------------------------------------------------------------------------------+
| Description of Services                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in      |
|   order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS             |
|   requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness       |
|   Information Security Protection (CRISP) project.                                                                        |
|                                                                                                                           |
| - We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones  |
|   and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities      |
|   associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones    |
|   (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track     |
|   progress status of VA wide tasks.                                                                                       |
+---------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - FSS SHAREPOINT SUPPORT                                                                                                  |
|                                                                                                                           |
| ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for             |
| improvements in FSS business operations methods to achieve improvements in FSS business operations which included:        |
|                                                                                                                           |
| 1.  Business process or procedures changes                                                                                |
|                                                                                                                           |
| 2.  Application of pertinent Technology (e.g., SharePoint tracking)                                                       |
|                                                                                                                           |
| 3.  Ability to utilize SharePoint applications to improve business operations                                             |
|                                                                                                                           |
| We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation)   |
| that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As   |
| part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with   |
| links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other       |
| similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.                     |
|                                                                                                                           |
| - FSS CRISP PROGRAM SUPPORT                                                                                               |
|                                                                                                                           |
| ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components,     |
| milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of  |
| CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.   |
|                                                                                                                           |
| We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts      |
| (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with |
| CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, |
| action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress         |
| upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided       |
| weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications    |
| System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues,   |
| risks and accomplishments associated with FSS CRISP sustainment.                                                          |
|                                                                                                                           |
| We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS    |
| Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks,    |
| and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS         |
| Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues     |
| associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All  |
| weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any     |
| distribution.                                                                                                             |
|                                                                                                                           |
| - GOVERNANCE SUPPORT                                                                                                      |
|                                                                                                                           |
| ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to      |
| include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and    |
| workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments,  |
| delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and       |
| potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which    |
| measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics |
| were maintained and continuously revised, by us, as the need for change arises. We provided project governance management |
| expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported   |
| the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization,    |
| and present it to FSS leadership for review and approval.                                                                 |
|                                                                                                                           |
| As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and     |
| facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned   |
| with FSS mission and goals.                                                                                               |
|                                                                                                                           |
| - FSS REQUIREMENTS ELABORATION PLANNING SUPPORT                                                                           |
|                                                                                                                           |
| As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements    |
| Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan     |
| includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and    |
| OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS     |
| Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in   |
| the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and      |
| channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and          |
| communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and      |
| approval from the COR/FSS VA PM.                                                                                          |
|                                                                                                                           |
| We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all  |
| FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future            |
| activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided        |
| communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.                           |
|                                                                                                                           |
| - FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which    |
|   includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet        |
|   postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor      |
|   these correspondence/communication products as needed.                                                                  |
|                                                                                                                           |
| - Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders       |
|   understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities |
|   in implementing medical/special purpose device security.                                                                |
|                                                                                                                           |
| - FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements  |
|   for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance  |
|   to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; |
|   FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and         |
|   Vanguard Magazine.                                                                                                      |
|                                                                                                                           |
| - FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS        |
|   Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, |
|   support topic research, capture notes, poll the group on topics, and conduct live meetings.                             |
|                                                                                                                           |
| - FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security    |
|   matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and |
|   ad hoc training on new security initiatives.                                                                            |
+---------------------------------------------------------------------------------------------------------------------------+

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC                                                                                                                                                                     |
+================+================+================+================+================+================+================+================+================+================+
| Contracting Agency / Business                    | Department of Veterans Affairs                                                                                       |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Applicable Contract Number                       |                                                  | Total Dollar Value                               |                |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Contract Name                                    | Security Control Assessments                                                                                         |
+----------------+---------------------------------+---------------------------------+--------------------------------------------------+---------------------------------+
| Contract Type  | Firm Fixed Price (FFP)                                            | Place of Performance                             | Remote Locations                |
+----------------+----------------+---------------------------------+----------------+---------------------------------+----------------+---------------------------------+
| Prime or Subcontractor          |                                 | Period of Performance                            | 12 months from date of award, with four 12-month |
|                                 |                                 |                                                  | option periods.                                  |
+---------------------------------+---------------------------------+--------------------------------------------------+--------------------------------------------------+
| POC                             |                                                                                                                                       |
+---------------------------------+---------------------------------------------------------------------------------------------------------------------------------------+
| Background                                                                                                                                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and      |
| services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services  |
| to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and      |
| compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals.                                                |
|                                                                                                                                                                         |
| During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector       |
| General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls,              |
| configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously         |
| identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support        |
| Federal regulatory requirements and the Federal Government oversight and audit community.                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Scope of Work                                                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC performed:                                                                                                                                                          |
|                                                                                                                                                                         |
| 1.  Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented   |
|     correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting  |
|     security requirements.                                                                                                                                              |
|                                                                                                                                                                         |
| a)  Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the      |
|     test.                                                                                                                                                               |
|                                                                                                                                                                         |
| b)  Execute the SAP.                                                                                                                                                    |
|                                                                                                                                                                         |
| c)  Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).                           |
|                                                                                                                                                                         |
| 2.  Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within   |
|     or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall          |
|     effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with      |
|     respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the       |
|     information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above               |
|     responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.      |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                                                                 |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Contractor Independent Assessment                                                                                                                                       |
|                                                                                                                                                                         |
| ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information    |
| systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because  |
| SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an        |
| assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their         |
| environment of operation and recommend corrective actions to address identified vulnerabilities.                                                                        |
|                                                                                                                                                                         |
| ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization     |
| (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).   |
|                                                                                                                                                                         |
| ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed.                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment Preparation                                                                                                                                 |
|                                                                                                                                                                         |
| - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements |
|   captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to   |
|   develop a System Rules of Engagement (ROE) Agreement.                                                                                                                 |
|                                                                                                                                                                         |
| The ROE must correctly identify the following:                                                                                                                          |
|                                                                                                                                                                         |
| 1)  Scope of testing                                                                                                                                                    |
|                                                                                                                                                                         |
| a)  ABC only tested for controls within scope of system type, location, and within the appropriate management control                                                   |
|                                                                                                                                                                         |
| 2)  Network ranges being assessed                                                                                                                                       |
|                                                                                                                                                                         |
| 3)  System components being assessed                                                                                                                                    |
|                                                                                                                                                                         |
| 4)  Locations being assessed                                                                                                                                            |
|                                                                                                                                                                         |
| 5)  SCA and all members conducting assessments including systems being used                                                                                             |
|                                                                                                                                                                         |
| 6)  Assessment type and method                                                                                                                                          |
|                                                                                                                                                                         |
| 7)  Tools used for the assessment                                                                                                                                       |
|                                                                                                                                                                         |
| 8)  A detailed list of artifacts or evidence not of record with the system and needed to support the SCA                                                                |
|                                                                                                                                                                         |
| - ABC developed and submitted a SCA Test Plan which:                                                                                                                    |
|                                                                                                                                                                         |
| 1.  Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance    |
|     reviews), resources, and milestones for the system being tested.                                                                                                    |
|                                                                                                                                                                         |
| 2.  Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities.                         |
|                                                                                                                                                                         |
| 3.  Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA.                     |
|                                                                                                                                                                         |
| 4.  Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were   |
|     used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.                                                                   |
|                                                                                                                                                                         |
| 5.  Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all          |
|     necessary parties.                                                                                                                                                  |
|                                                                                                                                                                         |
| - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and       |
|   procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems       |
|   needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security      |
|   categorization.                                                                                                                                                       |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment                                                                                                                                             |
|                                                                                                                                                                         |
| - ABC performed the SCA according to the processes and procedures described in the SAP.                                                                                 |
|                                                                                                                                                                         |
| - ABC completed the following communication and reporting activities:                                                                                                   |
|                                                                                                                                                                         |
| 1.  SCA Pre-Site Meeting                                                                                                                                                |
|                                                                                                                                                                         |
| 2.  System Component Assessment Daily Status                                                                                                                            |
|                                                                                                                                                                         |
| 3.  Weekly Status Report                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  SCA Out-Brief Meeting                                                                                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Assessment Report                                                                                                                                              |
|                                                                                                                                                                         |
| - ABC developed the Security Assessment Report to include the following:                                                                                                |
|                                                                                                                                                                         |
| 1.  Documentation of each SCA                                                                                                                                           |
|                                                                                                                                                                         |
| 2.  Assessment test objectives as identified in NIST SP 800-53A                                                                                                         |
|                                                                                                                                                                         |
| 3.  Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A                                                                             |
|                                                                                                                                                                         |
| 4.  All software and hardware components assessed                                                                                                                       |
|                                                                                                                                                                         |
| 5.  Sequential, step-by-step assessment procedures for testing each test objective                                                                                      |
|                                                                                                                                                                         |
| 6.  Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment      |
|                                                                                                                                                                         |
| 7.  Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices        |
|                                                                                                                                                                         |
| 8.  Rationale for any system or device in the inventory not directly tested                                                                                             |
|                                                                                                                                                                         |
| 9.  Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed,     |
|     location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark                                         |
|                                                                                                                                                                         |
| 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or     |
|     “Other Than Satisfied” determination                                                                                                                                |
|                                                                                                                                                                         |
| 11. Results of Findings to be documented in the OCS POA&M Import Template (data                                                                                         |
|                                                                                                                                                                         |
| defined file in Excel format)                                                                                                                                           |
|                                                                                                                                                                         |
| 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual |
|     results for each system component type                                                                                                                              |
|                                                                                                                                                                         |
| 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO                                                         |
|                                                                                                                                                                         |
| - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and      |
|   reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)                              |
|                                                                                                                                                                         |
| - ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA |
|   Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned    |
|   were collected from the following individuals:                                                                                                                        |
|                                                                                                                                                                         |
| 1.  AO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 2.  SO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 3.  ISSO                                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  IT System Manager/System Steward                                                                                                                                    |
|                                                                                                                                                                         |
| 5.  Security Controls Assessor                                                                                                                                          |
|                                                                                                                                                                         |
| 6.  VA Program Manager                                                                                                                                                  |
|                                                                                                                                                                         |
| 7.  COR                                                                                                                                                                 |
|                                                                                                                                                                         |
| 8.  CO                                                                                                                                                                  |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Ongoing Security Control Assessments                                                                                                                                    |
|                                                                                                                                                                         |
| - ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks                           |
|                                                                                                                                                                         |
| - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.                                                             |
|                                                                                                                                                                         |
| - We performed all required communications and reporting activities as required by RMF Tasks                                                                            |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+



=================== Prompt for Section 9: Key Personnel Resumes ====================

Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** Key Personnel Resumes  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: [][A picture containing text, clipart Description automatically
generated][Logo Description automatically generated][A picture
containing shape Description automatically generated][][][][]

Volume I – Technical

“This proposal includes data that shall not be disclosed outside the
Government and shall not be duplicated, used or disclosed in whole or in
part for any purpose other than to evaluate this proposal. If, however,
a Contract is awarded to this Offeror as a result of or in connection
with the submission of this data, the Government shall have the right to
duplicate, use, or disclose the data to the extent provided in the
resulting Contract. This restriction does not limit the Government’s
right to use information contained in this data if it is obtained from
another source without restriction. The data subject to the restriction
is contained in all sheets of this proposal.”

Office of the Comptroller of the Currency (OCC)

Cyber Security Office (CSO)

Cybersecurity Assessment and Compliance (CA&C)

Solicitation Number: 2031JW22Q00022

Due Date: 10:00 AM (EST), October 29, 2021

October 29, 2021

Solicitation Number: 2031JW22Q00022

Agency: Office of the Comptroller of the Currency (OCC)

Subject: Team Greenbrier Response to Cybersecurity Assessment and
Compliance (CA&C)

Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter
referred to as “Team Greenbrier”) are pleased to submit this response to
the Office of the Comptroller of the Currency (OCC), Cybersecurity
Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion
for solving problems and ensuring the sustainability of results through
project leadership extends across multiple industries. Team Greenbrier’s
substantial past performance and reputation for excellence with our
clients attest to the fact that we continually deliver low-risk,
high-quality, value-added strategic support services.

Team Greenbrier has been providing Cybersecurity Assessment and
Compliance services for many years to a large base of clients in the
federal government and commercial space as part of projects that are
similar in size and scope to the work that will be part of the OCC
program. Our deep subject matter expertise in Cybersecurity Assessment,
Compliance, and experience in providing innovative services with proven
program management skills will foster the support needed to ensure the
best quality services.

We are enthusiastic about the opportunity to work with you and eager to
demonstrate immediately the positive impact we can have on your
organization. Team Greenbrier acknowledges receipt of the RFQ and
Amendments and takes no exception to the terms and conditions. Our
company information includes:

The information of teaming partner includes:

+---------------------------------------+------------------------------+
| - Company Name:                       | - DUNS Number:               |
+=======================================+==============================+

We are personally committed to the success of this program and agree
with all terms, conditions, & provisions and to furnish any or all items
included in the solicitation. With our dynamic leadership, supervision,
and more than capable personnel, we are confident that we are ideally
suited to fulfill your needs. If you have any questions or need
additional information, please contact our primary point of contact.

Respectfully,

[]

Scotty Johnson

President

Table of Contents

1 Assumptions 1

1.1 Technical Assumptions 1

1.2 Price Assumptions 1

2 SF 1449 1

3 Acknowledgment of Solicitation Amendments 1

4 Completion of Representations: 1

5 Attestation 1

6 Conflict of Interest Mitigation Plan 1

7 GSA Federal Supply Schedule (FSS) 1

8 Technical/Management Approach (No more than 15 pages) 2

8.1 Methodology/process for testing web applications, financial systems,
network and infrastructure devices and end-user devices, including a
detailed process for manual and automated testing 2

8.2 Methodology for assigning risk ratings to weaknesses discovered
during the assessment process 2

8.3 Process for audit preparation and tracking, distributing, and
responding to data call requests associated with annual external audits
(FISCAM, FISMA and A-123) 2

8.4 Process for integrating the Risk Management Framework (RMF) –
Monitor step with an eGRC tool 2

8.5 Process for automating assessments 2

8.6 Methodology for tracking, reporting and completing all work
identified in the PWS 2

8.7 Staffing plan and approach for obtaining and maintaining individuals
with the qualifications listed in the Key Personnel section 2

9 Key Personnel Resumes (No more than 3 pages per resume) 2

9.1 Project Manager 2

9.2 Cybersecurity Assurance & Compliance Program Support Lead 3

9.3 Security & Privacy Control Assessor Lead 3

9.4 Quality Assurance Lead 4

9.5 Tier 2 eGRC Specialist 4

9.6 Automated Assessment Developer 4

9.7 IT Security Subject Matter Expert 5

10 Past Performance 5

Assumptions

Technical Assumptions

<Insert Assumptions>

Price Assumptions

<Insert Assumptions>

SF 1449

Completed by the Quoter and signed by an authorized official of the
company.

<Insert SF 1449>

Acknowledgment of Solicitation Amendments

<Insert Response>

Completion of Representations:

OCC provision 1052.209-8001, Conflict of Interest Disclosure and
Certification (Feb 2014)

<Insert Reps and Certs>

Attestation

Attest by stating in its quote submission that “All quoted contractor
personnel meet Contractor Qualifications and Key Personnel outlined in
Section II, “Qualifications for Tasks/Key Personnel” in the SOW.

<Insert Response>

Conflict of Interest Mitigation Plan

Provide a description of any actual or potential conflicts of interest
related to this requirement. If the Quoter has identified any actual or
potential conflicts, the Quoter shall provide a mitigation plan. If
Quoter has identified no actual or potential conflicts of interest,
quoter shall provide a statement in writing to that affect (this can be
satisfied with completion of OCC Provision 1052.209-8001). Quoters may
submit a single OCI statement, however, this does not alleviate the
prime contractor’s responsibility to research potential conflicts of any
subcontractors and submit a mitigation strategy, if applicable.

<Insert Response>

GSA Federal Supply Schedule (FSS)

The vendor must explicitly confirm that all proposed services fall under
a vendor’s GSA Federal Supply Schedule (FSS). If the vendor has a
teaming arrangement with another vendor to provide any services in the
vendor’s quotation, please identify the vendor and the GSA FSS number
for those affected CLINs.

<Insert Response>

Technical/Management Approach (No more than 15 pages)

Contractors shall provide a technical/management approach that conveys
an understanding of the requirement and includes:

Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

<Insert Response>

Methodology for assigning risk ratings to weaknesses discovered during the assessment process

<Insert Response>

Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

<Insert Response>

Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

<Insert Response>

Process for automating assessments

<Insert Response>

Methodology for tracking, reporting and completing all work identified in the PWS

<Insert Response>

Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

<Insert Response>

Key Personnel Resumes (No more than 3 pages per resume)

The Quoter shall submit resumes based on the individual’s experience and
certifications, for each of the Key Personnel identified in the SOW. The
Quoter shall also provide a letter of commitment for each proposed key
personnel.

Project Manager

The Project Manager shall meet the following minimum criteria:

- At least eight (8) years of experience in program and project
  management supporting information security or cybersecurity projects
  for the federal government is required.

- A bachelor's degree from an accredited college and a Project
  Management Professional (PMP) or equivalent (as approved by the OCC
  COR) is required.

- Hands-on experience using the Microsoft Office Project is required.

- Experience with NIST Risk Management Framework and Governance, Risk &
  Compliance (GRC) and Information Assurance capabilities/tools (e.g.,
  ServiceNow GRC, RSA Archer, CSAM, Xacta, etc.) is required.

- Certified Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified Information Systems Security
  Professional (CISSP), or Certified Information Security Manager (CISM)
  is required.

<Insert Resume>

Cybersecurity Assurance & Compliance Program Support Lead

The Cybersecurity Assurance and Compliance Program Support Lead shall
meet the following minimum criteria, unless otherwise noted as
preferred:

- At least three (3) years of experience managing security compliance
  program support teams is required.

- At least six (6) years of experience developing RMF documentation in
  the last five years, including but not limited to: Standard Operating
  Procedures (SOPs), system security plans (SSPs), program plans,
  processes, workflows are required.

- At least eight (8) years of Information Security experience is
  required.

- At least two (2) years of experience using eGRC tools is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified in Risk and Information Systems Control
  (CRISC), or Certified Information Security Manager (CISM) is required.

- Experience with ServiceNow GRC CAM tool is preferred.

<Insert Resume>

Security & Privacy Control Assessor Lead

The Security and Privacy Control Assessor Lead shall meet the following
minimum criteria, unless otherwise noted as preferred:

- Three (3) years of experience managing security assessment teams is
  required.

- Six (6) years of experience performing detailed, full-scope technical
  security control testing for each of the component types listed in
  Section C.3.1, including development of security and privacy
  assessment plans is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with the use of eGRC tools is required.
  Experience with ServiceNow is preferred.

- Experience working with Qualys Enterprise, Archer, Nessus, HCL
  AppScan, and technical outputs of the Kali Linux suite is required.

- Advanced knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Ethical Hacker (CEH), Certified Risk and Information Systems Control
  (CRISC), or Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Quality Assurance Lead

The Quality Assurance (QA) Lead shall meet the following minimum
criteria, unless otherwise noted as preferred:

- Three (3) years of managing technical security QA team/SA&A Package
  Independent Validation & Verification (IV&V) is required.

- Six (6) years of experience developing RMF documentation is required.

- Six (6) years of experience conducting security and privacy control
  assessments is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with eGRC tools is required. Experience
  with ServiceNow GRC tool suite, including CAM, is preferred.

- Certified in Certified Information Systems Security Professional
  (CISSP), Certified Risk and Information Systems Control (CRISC), or
  Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Tier 2 eGRC Specialist

The Tier 2 eGRC Specialist shall meet the following minimum criteria,
unless otherwise noted as preferred:

- At least two (2) years of experience with developing/writing advanced
  custom ServiceNow script includes, user interface actions, user
  interface policies, access control lists, client scripts, scheduled
  jobs, data tables and data fields in the last three years is required.

- At least three (3) years of Integrated Risk Management (IRM)/GRC
  implementation experience in the last four years is required; GRC CAM
  experience is preferred. ServiceNow Certified Risk & Compliance
  implementation specialist is preferred.

- At least (2) years of experience configuring and customizing the ITSM
  suite, IT Operations Management suite, and NOW Platform Capabilities
  in the last three years is required.

- At least (2) years of experience with technical components such as
  LDAP, Web Services, REST, SOAP, APIs, XML, JavaScript in the last
  three years is required.

- At least (2) years of experience with ITILv3 Service Management
  processes in the last three years is required.

- At least three (3) years of Administrator/Developer experience in the
  last four years is required; in addition, GRC ServiceNow Certified
  Developer is preferred.

- At least two (2) years of ServiceNow Implementation Specialist
  experience in the last three years is required; in addition, Certified
  ServiceNow Implementation Specialist is preferred.

<Insert Resume>

Automated Assessment Developer

The Automated Assessment Developer shall meet the following minimum
criteria:

- Five (5) years of experience as a software developer is required,

- Three (3) years of experience integrating cybersecurity tools,
  including Qualys, RSA Archer, and Splunk with ServiceNow GRC and
  ServiceNow formats and languages listed in Task C.2.7 is preferred.

- Bachelor of Science degree in Computer Science is required.

<Insert Resume>

IT Security Subject Matter Expert

The IT Security Subject-Matter Expert (SME), who handles areas requiring
elevated technical security skills (e.g., risk analysis of cutting-edge
technology, or high visibility project that requires a SME review due to
quick turn around with accurate results, etc.), and technical escalation
concerns that may arise in the implementation of new IT security and/or
compliance program requirements, security assessments, technical SA&A
package evaluations, etc., shall meet the following minimum criteria,
unless otherwise noted as preferred:

- Ten (10) years of IT security experience is required.

- Five (5) years of experience performing detailed, full-scope technical
  control testing for the component types listed in Section C.3.1 of
  this SOW including development security assessment plans is required.

- In-depth knowledge of and experience in implementing and using GRC
  platforms is required. Experience with ServiceNow GRC tool suite,
  including CAM preferred.

- Hands-on experience working with at least four (4) of the following
  seven (7) technology products: Forescout CounterAct, ArcSight, HCL
  BigFix, Sailpoint, CyberArk, RES, and Splunk is required.

- Extensive knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Bachelor of Science degree in Computer Science, Cybersecurity, or
  Information Systems. is required.

- Certified Information Systems Security Professional (CISSP) is
  required.

- Certified Cloud Security Professional (CCSP) is preferred.

<Insert Resume>

Past Performance

The Quoter shall request that references fill out and return three (3)
Past Performance Questionnaire Survey (Attachment 3). Quoters may not
submit the completed survey forms to the OCC Contract Specialist on
behalf of their references; instead, the completed forms shall be
returned directly by the referenced customers to the OCC Contract
Specialist, per the information provided on the form. The forms are due
to the OCC no later than the specified deadline for submitting
quotations in response to this RFQ. The OCC may also obtain and consider
any other relevant information in evaluating past performance, including
information contained in Contractor Performance Assessment Reporting
System (CPAR).
  
- Section Outline: Key Personnel Resumes  
- RFP Summary: 
Proposal Info
•  Solicitation Number: RFQ 2031JW22Q00022 [Page 17]
•  NAICS Code: 541519 [Page 51]
•  Name/Title: Cybersecurity Assessment and Compliance (CA&C) Support [Page 1]
•  Solicitation Type: RFQ [Page 17]
•  Department/Agency Name: Comptroller of the Currency [Page 1]
•  Inquiries/Questions Due Date and Time: 12:00 PM (EST), October 12, 2021 [Page
51]
•  Proposal Due Date and Time: 10:00 AM (EST), October 29, 2021 [Page 50]
•  Mode of Submission: eBuy [Page 50]
•  Place of Performance: Online [Page 50]
•  Point of Contact (POC):
○  Primary: <EMAIL> [Page 51]
○  Secondary: <EMAIL> [Page 51]
•  Set Aside: Small Business [Page 1]
•  Period of Performance:
○  Base Period: 12 months, 01/03/2022 to 01/02/2023 [Page 2]
○  Option Period 1: 12 months, 01/03/2023 to 01/02/2024 [Page 3]
○  Option Period 2: 12 months, 01/03/2024 to 01/02/2025 [Page 4]
○  Option Period 3: 12 months, 01/03/2025 to 01/02/2026 [Page 5]
○  Option Period 4: 12 months, 01/03/2026 to 01/02/2027 [Page 5]
•  Key Personnel:
○  Project Manager [Page 22]
○  Cybersecurity Assurance and Compliance Program Support Lead [Page 22]
○  Security and Privacy Control Assessor Lead [Page 23]
○  Quality Assurance Lead [Page 24]
○  Tier 2 eGRC Specialist [Page 23]
○  Automated Assessment Developer [Page 23]
○  IT Security Subject Matter Expert [Page 23]
•  Security Clearance Requirements: N/A [Page 49]
•  Task Order Type: N/A [Page 50]
Purpose
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). [Section II - Statement of Work (SOW)] The CA&C
Program includes agency-level oversight for compliance with the Risk Management
Framework (RMF), Audit Liaison, and execution of Assessment and Authorization steps of the
RMF. The scope of work shall comprise: Project Management and Reporting; Cyber
Assurance and Compliance (CA&C) Program Support, including, Audit Liaison, Compliance
Oversight activities, CA&C program security documentation, and technical support for
automation and program tools; Security & Privacy Control Assessments of new and existing
OCC systems; Quality Assurance; and Administrative and IT Security Subject Matter Expert
Support. [Section II - Statement of Work (SOW)]
Scope
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). The CA&C Program includes agency-level oversight
for compliance with the Risk Management Framework (RMF), Audit Liaison, and execution of
Assessment and Authorization steps of the RMF. The scope of work shall comprise: Project
Management and Reporting; Cyber Assurance and Compliance (CA&C) Program Support,
including, Audit Liaison, Compliance Oversight activities, CA&C program security
documentation, and technical support for automation and program tools; Security & Privacy
Control Assessments of new and existing OCC systems; Quality Assurance; and
Administrative and IT Security Subject Matter Expert Support.
Task Areas Overview
•  C.1 Project Management and Reporting
•  C.2 Cyber Assurance and Compliance Program Support
•  C.3 Security and Privacy Control Assessments
•  C.4 Quality Assurance
•  C.5 Administrative Support
•  C.6 IT Security Subject Matter Expert Support

  
- Knowledge Base: +---------------------------------------------------------------------------------------------------------------------------+
| ABC Government Solutions, Inc                                                                                             |
+==================+==============+==============+==============+==============+==============+==============+==============+
| Contracting Agency / Business   | Department of Veterans Affairs                                                          |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Applicable Contract Number      |                             | Total Dollar Value          |                             |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Contract Name                   | FSS Program Management Support (HMS Technologies)                                       |
+---------------------------------+-----------------------------+--------------------------------------------+--------------+
| Contract Type                   | FFP                         | Place of Performance                       | Remote       |
+------------------+--------------+--------------+--------------+--------------+-----------------------------+--------------+
| Prime or         | Subcontractor               | Period of Performance       |                                            |
| Subcontractor    |                             |                             |                                            |
+------------------+-----------------------------+-----------------------------+--------------------------------------------+
| POC              |                                                                                                        |
+------------------+--------------------------------------------------------------------------------------------------------+
| Description of Services                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in      |
|   order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS             |
|   requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness       |
|   Information Security Protection (CRISP) project.                                                                        |
|                                                                                                                           |
| - We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones  |
|   and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities      |
|   associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones    |
|   (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track     |
|   progress status of VA wide tasks.                                                                                       |
+---------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - FSS SHAREPOINT SUPPORT                                                                                                  |
|                                                                                                                           |
| ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for             |
| improvements in FSS business operations methods to achieve improvements in FSS business operations which included:        |
|                                                                                                                           |
| 1.  Business process or procedures changes                                                                                |
|                                                                                                                           |
| 2.  Application of pertinent Technology (e.g., SharePoint tracking)                                                       |
|                                                                                                                           |
| 3.  Ability to utilize SharePoint applications to improve business operations                                             |
|                                                                                                                           |
| We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation)   |
| that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As   |
| part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with   |
| links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other       |
| similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.                     |
|                                                                                                                           |
| - FSS CRISP PROGRAM SUPPORT                                                                                               |
|                                                                                                                           |
| ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components,     |
| milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of  |
| CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.   |
|                                                                                                                           |
| We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts      |
| (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with |
| CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, |
| action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress         |
| upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided       |
| weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications    |
| System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues,   |
| risks and accomplishments associated with FSS CRISP sustainment.                                                          |
|                                                                                                                           |
| We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS    |
| Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks,    |
| and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS         |
| Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues     |
| associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All  |
| weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any     |
| distribution.                                                                                                             |
|                                                                                                                           |
| - GOVERNANCE SUPPORT                                                                                                      |
|                                                                                                                           |
| ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to      |
| include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and    |
| workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments,  |
| delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and       |
| potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which    |
| measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics |
| were maintained and continuously revised, by us, as the need for change arises. We provided project governance management |
| expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported   |
| the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization,    |
| and present it to FSS leadership for review and approval.                                                                 |
|                                                                                                                           |
| As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and     |
| facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned   |
| with FSS mission and goals.                                                                                               |
|                                                                                                                           |
| - FSS REQUIREMENTS ELABORATION PLANNING SUPPORT                                                                           |
|                                                                                                                           |
| As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements    |
| Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan     |
| includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and    |
| OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS     |
| Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in   |
| the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and      |
| channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and          |
| communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and      |
| approval from the COR/FSS VA PM.                                                                                          |
|                                                                                                                           |
| We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all  |
| FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future            |
| activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided        |
| communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.                           |
|                                                                                                                           |
| - FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which    |
|   includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet        |
|   postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor      |
|   these correspondence/communication products as needed.                                                                  |
|                                                                                                                           |
| - Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders       |
|   understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities |
|   in implementing medical/special purpose device security.                                                                |
|                                                                                                                           |
| - FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements  |
|   for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance  |
|   to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; |
|   FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and         |
|   Vanguard Magazine.                                                                                                      |
|                                                                                                                           |
| - FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS        |
|   Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, |
|   support topic research, capture notes, poll the group on topics, and conduct live meetings.                             |
|                                                                                                                           |
| - FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security    |
|   matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and |
|   ad hoc training on new security initiatives.                                                                            |
+---------------------------------------------------------------------------------------------------------------------------+

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC                                                                                                                                                                     |
+================+================+================+================+================+================+================+================+================+================+
| Contracting Agency / Business                    | Department of Veterans Affairs                                                                                       |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Applicable Contract Number                       |                                                  | Total Dollar Value                               |                |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Contract Name                                    | Security Control Assessments                                                                                         |
+----------------+---------------------------------+---------------------------------+--------------------------------------------------+---------------------------------+
| Contract Type  | Firm Fixed Price (FFP)                                            | Place of Performance                             | Remote Locations                |
+----------------+----------------+---------------------------------+----------------+---------------------------------+----------------+---------------------------------+
| Prime or Subcontractor          |                                 | Period of Performance                            | 12 months from date of award, with four 12-month |
|                                 |                                 |                                                  | option periods.                                  |
+---------------------------------+---------------------------------+--------------------------------------------------+--------------------------------------------------+
| POC                             |                                                                                                                                       |
+---------------------------------+---------------------------------------------------------------------------------------------------------------------------------------+
| Background                                                                                                                                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and      |
| services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services  |
| to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and      |
| compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals.                                                |
|                                                                                                                                                                         |
| During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector       |
| General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls,              |
| configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously         |
| identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support        |
| Federal regulatory requirements and the Federal Government oversight and audit community.                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Scope of Work                                                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC performed:                                                                                                                                                          |
|                                                                                                                                                                         |
| 1.  Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented   |
|     correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting  |
|     security requirements.                                                                                                                                              |
|                                                                                                                                                                         |
| a)  Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the      |
|     test.                                                                                                                                                               |
|                                                                                                                                                                         |
| b)  Execute the SAP.                                                                                                                                                    |
|                                                                                                                                                                         |
| c)  Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).                           |
|                                                                                                                                                                         |
| 2.  Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within   |
|     or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall          |
|     effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with      |
|     respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the       |
|     information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above               |
|     responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.      |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                                                                 |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Contractor Independent Assessment                                                                                                                                       |
|                                                                                                                                                                         |
| ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information    |
| systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because  |
| SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an        |
| assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their         |
| environment of operation and recommend corrective actions to address identified vulnerabilities.                                                                        |
|                                                                                                                                                                         |
| ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization     |
| (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).   |
|                                                                                                                                                                         |
| ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed.                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment Preparation                                                                                                                                 |
|                                                                                                                                                                         |
| - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements |
|   captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to   |
|   develop a System Rules of Engagement (ROE) Agreement.                                                                                                                 |
|                                                                                                                                                                         |
| The ROE must correctly identify the following:                                                                                                                          |
|                                                                                                                                                                         |
| 1)  Scope of testing                                                                                                                                                    |
|                                                                                                                                                                         |
| a)  ABC only tested for controls within scope of system type, location, and within the appropriate management control                                                   |
|                                                                                                                                                                         |
| 2)  Network ranges being assessed                                                                                                                                       |
|                                                                                                                                                                         |
| 3)  System components being assessed                                                                                                                                    |
|                                                                                                                                                                         |
| 4)  Locations being assessed                                                                                                                                            |
|                                                                                                                                                                         |
| 5)  SCA and all members conducting assessments including systems being used                                                                                             |
|                                                                                                                                                                         |
| 6)  Assessment type and method                                                                                                                                          |
|                                                                                                                                                                         |
| 7)  Tools used for the assessment                                                                                                                                       |
|                                                                                                                                                                         |
| 8)  A detailed list of artifacts or evidence not of record with the system and needed to support the SCA                                                                |
|                                                                                                                                                                         |
| - ABC developed and submitted a SCA Test Plan which:                                                                                                                    |
|                                                                                                                                                                         |
| 1.  Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance    |
|     reviews), resources, and milestones for the system being tested.                                                                                                    |
|                                                                                                                                                                         |
| 2.  Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities.                         |
|                                                                                                                                                                         |
| 3.  Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA.                     |
|                                                                                                                                                                         |
| 4.  Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were   |
|     used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.                                                                   |
|                                                                                                                                                                         |
| 5.  Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all          |
|     necessary parties.                                                                                                                                                  |
|                                                                                                                                                                         |
| - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and       |
|   procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems       |
|   needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security      |
|   categorization.                                                                                                                                                       |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment                                                                                                                                             |
|                                                                                                                                                                         |
| - ABC performed the SCA according to the processes and procedures described in the SAP.                                                                                 |
|                                                                                                                                                                         |
| - ABC completed the following communication and reporting activities:                                                                                                   |
|                                                                                                                                                                         |
| 1.  SCA Pre-Site Meeting                                                                                                                                                |
|                                                                                                                                                                         |
| 2.  System Component Assessment Daily Status                                                                                                                            |
|                                                                                                                                                                         |
| 3.  Weekly Status Report                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  SCA Out-Brief Meeting                                                                                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Assessment Report                                                                                                                                              |
|                                                                                                                                                                         |
| - ABC developed the Security Assessment Report to include the following:                                                                                                |
|                                                                                                                                                                         |
| 1.  Documentation of each SCA                                                                                                                                           |
|                                                                                                                                                                         |
| 2.  Assessment test objectives as identified in NIST SP 800-53A                                                                                                         |
|                                                                                                                                                                         |
| 3.  Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A                                                                             |
|                                                                                                                                                                         |
| 4.  All software and hardware components assessed                                                                                                                       |
|                                                                                                                                                                         |
| 5.  Sequential, step-by-step assessment procedures for testing each test objective                                                                                      |
|                                                                                                                                                                         |
| 6.  Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment      |
|                                                                                                                                                                         |
| 7.  Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices        |
|                                                                                                                                                                         |
| 8.  Rationale for any system or device in the inventory not directly tested                                                                                             |
|                                                                                                                                                                         |
| 9.  Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed,     |
|     location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark                                         |
|                                                                                                                                                                         |
| 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or     |
|     “Other Than Satisfied” determination                                                                                                                                |
|                                                                                                                                                                         |
| 11. Results of Findings to be documented in the OCS POA&M Import Template (data                                                                                         |
|                                                                                                                                                                         |
| defined file in Excel format)                                                                                                                                           |
|                                                                                                                                                                         |
| 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual |
|     results for each system component type                                                                                                                              |
|                                                                                                                                                                         |
| 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO                                                         |
|                                                                                                                                                                         |
| - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and      |
|   reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)                              |
|                                                                                                                                                                         |
| - ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA |
|   Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned    |
|   were collected from the following individuals:                                                                                                                        |
|                                                                                                                                                                         |
| 1.  AO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 2.  SO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 3.  ISSO                                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  IT System Manager/System Steward                                                                                                                                    |
|                                                                                                                                                                         |
| 5.  Security Controls Assessor                                                                                                                                          |
|                                                                                                                                                                         |
| 6.  VA Program Manager                                                                                                                                                  |
|                                                                                                                                                                         |
| 7.  COR                                                                                                                                                                 |
|                                                                                                                                                                         |
| 8.  CO                                                                                                                                                                  |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Ongoing Security Control Assessments                                                                                                                                    |
|                                                                                                                                                                         |
| - ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks                           |
|                                                                                                                                                                         |
| - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.                                                             |
|                                                                                                                                                                         |
| - We performed all required communications and reporting activities as required by RMF Tasks                                                                            |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+



=================== Prompt for Section 10: Past Performance ====================

Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** Past Performance  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: [][A picture containing text, clipart Description automatically
generated][Logo Description automatically generated][A picture
containing shape Description automatically generated][][][][]

Volume I – Technical

“This proposal includes data that shall not be disclosed outside the
Government and shall not be duplicated, used or disclosed in whole or in
part for any purpose other than to evaluate this proposal. If, however,
a Contract is awarded to this Offeror as a result of or in connection
with the submission of this data, the Government shall have the right to
duplicate, use, or disclose the data to the extent provided in the
resulting Contract. This restriction does not limit the Government’s
right to use information contained in this data if it is obtained from
another source without restriction. The data subject to the restriction
is contained in all sheets of this proposal.”

Office of the Comptroller of the Currency (OCC)

Cyber Security Office (CSO)

Cybersecurity Assessment and Compliance (CA&C)

Solicitation Number: 2031JW22Q00022

Due Date: 10:00 AM (EST), October 29, 2021

October 29, 2021

Solicitation Number: 2031JW22Q00022

Agency: Office of the Comptroller of the Currency (OCC)

Subject: Team Greenbrier Response to Cybersecurity Assessment and
Compliance (CA&C)

Greenbrier Government Solutions, Inc. (Greenbrier) and Tista (hereafter
referred to as “Team Greenbrier”) are pleased to submit this response to
the Office of the Comptroller of the Currency (OCC), Cybersecurity
Assessment and Compliance (CA&C), RFQ number 2031JW22Q00022. Our passion
for solving problems and ensuring the sustainability of results through
project leadership extends across multiple industries. Team Greenbrier’s
substantial past performance and reputation for excellence with our
clients attest to the fact that we continually deliver low-risk,
high-quality, value-added strategic support services.

Team Greenbrier has been providing Cybersecurity Assessment and
Compliance services for many years to a large base of clients in the
federal government and commercial space as part of projects that are
similar in size and scope to the work that will be part of the OCC
program. Our deep subject matter expertise in Cybersecurity Assessment,
Compliance, and experience in providing innovative services with proven
program management skills will foster the support needed to ensure the
best quality services.

We are enthusiastic about the opportunity to work with you and eager to
demonstrate immediately the positive impact we can have on your
organization. Team Greenbrier acknowledges receipt of the RFQ and
Amendments and takes no exception to the terms and conditions. Our
company information includes:

The information of teaming partner includes:

+---------------------------------------+------------------------------+
| - Company Name:                       | - DUNS Number:               |
+=======================================+==============================+

We are personally committed to the success of this program and agree
with all terms, conditions, & provisions and to furnish any or all items
included in the solicitation. With our dynamic leadership, supervision,
and more than capable personnel, we are confident that we are ideally
suited to fulfill your needs. If you have any questions or need
additional information, please contact our primary point of contact.

Respectfully,

[]

Scotty Johnson

President

Table of Contents

1 Assumptions 1

1.1 Technical Assumptions 1

1.2 Price Assumptions 1

2 SF 1449 1

3 Acknowledgment of Solicitation Amendments 1

4 Completion of Representations: 1

5 Attestation 1

6 Conflict of Interest Mitigation Plan 1

7 GSA Federal Supply Schedule (FSS) 1

8 Technical/Management Approach (No more than 15 pages) 2

8.1 Methodology/process for testing web applications, financial systems,
network and infrastructure devices and end-user devices, including a
detailed process for manual and automated testing 2

8.2 Methodology for assigning risk ratings to weaknesses discovered
during the assessment process 2

8.3 Process for audit preparation and tracking, distributing, and
responding to data call requests associated with annual external audits
(FISCAM, FISMA and A-123) 2

8.4 Process for integrating the Risk Management Framework (RMF) –
Monitor step with an eGRC tool 2

8.5 Process for automating assessments 2

8.6 Methodology for tracking, reporting and completing all work
identified in the PWS 2

8.7 Staffing plan and approach for obtaining and maintaining individuals
with the qualifications listed in the Key Personnel section 2

9 Key Personnel Resumes (No more than 3 pages per resume) 2

9.1 Project Manager 2

9.2 Cybersecurity Assurance & Compliance Program Support Lead 3

9.3 Security & Privacy Control Assessor Lead 3

9.4 Quality Assurance Lead 4

9.5 Tier 2 eGRC Specialist 4

9.6 Automated Assessment Developer 4

9.7 IT Security Subject Matter Expert 5

10 Past Performance 5

Assumptions

Technical Assumptions

<Insert Assumptions>

Price Assumptions

<Insert Assumptions>

SF 1449

Completed by the Quoter and signed by an authorized official of the
company.

<Insert SF 1449>

Acknowledgment of Solicitation Amendments

<Insert Response>

Completion of Representations:

OCC provision 1052.209-8001, Conflict of Interest Disclosure and
Certification (Feb 2014)

<Insert Reps and Certs>

Attestation

Attest by stating in its quote submission that “All quoted contractor
personnel meet Contractor Qualifications and Key Personnel outlined in
Section II, “Qualifications for Tasks/Key Personnel” in the SOW.

<Insert Response>

Conflict of Interest Mitigation Plan

Provide a description of any actual or potential conflicts of interest
related to this requirement. If the Quoter has identified any actual or
potential conflicts, the Quoter shall provide a mitigation plan. If
Quoter has identified no actual or potential conflicts of interest,
quoter shall provide a statement in writing to that affect (this can be
satisfied with completion of OCC Provision 1052.209-8001). Quoters may
submit a single OCI statement, however, this does not alleviate the
prime contractor’s responsibility to research potential conflicts of any
subcontractors and submit a mitigation strategy, if applicable.

<Insert Response>

GSA Federal Supply Schedule (FSS)

The vendor must explicitly confirm that all proposed services fall under
a vendor’s GSA Federal Supply Schedule (FSS). If the vendor has a
teaming arrangement with another vendor to provide any services in the
vendor’s quotation, please identify the vendor and the GSA FSS number
for those affected CLINs.

<Insert Response>

Technical/Management Approach (No more than 15 pages)

Contractors shall provide a technical/management approach that conveys
an understanding of the requirement and includes:

Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

<Insert Response>

Methodology for assigning risk ratings to weaknesses discovered during the assessment process

<Insert Response>

Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

<Insert Response>

Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

<Insert Response>

Process for automating assessments

<Insert Response>

Methodology for tracking, reporting and completing all work identified in the PWS

<Insert Response>

Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

<Insert Response>

Key Personnel Resumes (No more than 3 pages per resume)

The Quoter shall submit resumes based on the individual’s experience and
certifications, for each of the Key Personnel identified in the SOW. The
Quoter shall also provide a letter of commitment for each proposed key
personnel.

Project Manager

The Project Manager shall meet the following minimum criteria:

- At least eight (8) years of experience in program and project
  management supporting information security or cybersecurity projects
  for the federal government is required.

- A bachelor's degree from an accredited college and a Project
  Management Professional (PMP) or equivalent (as approved by the OCC
  COR) is required.

- Hands-on experience using the Microsoft Office Project is required.

- Experience with NIST Risk Management Framework and Governance, Risk &
  Compliance (GRC) and Information Assurance capabilities/tools (e.g.,
  ServiceNow GRC, RSA Archer, CSAM, Xacta, etc.) is required.

- Certified Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified Information Systems Security
  Professional (CISSP), or Certified Information Security Manager (CISM)
  is required.

<Insert Resume>

Cybersecurity Assurance & Compliance Program Support Lead

The Cybersecurity Assurance and Compliance Program Support Lead shall
meet the following minimum criteria, unless otherwise noted as
preferred:

- At least three (3) years of experience managing security compliance
  program support teams is required.

- At least six (6) years of experience developing RMF documentation in
  the last five years, including but not limited to: Standard Operating
  Procedures (SOPs), system security plans (SSPs), program plans,
  processes, workflows are required.

- At least eight (8) years of Information Security experience is
  required.

- At least two (2) years of experience using eGRC tools is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Information Security Auditor (CISA), Certified Authorization
  Professional (CAP), Certified in Risk and Information Systems Control
  (CRISC), or Certified Information Security Manager (CISM) is required.

- Experience with ServiceNow GRC CAM tool is preferred.

<Insert Resume>

Security & Privacy Control Assessor Lead

The Security and Privacy Control Assessor Lead shall meet the following
minimum criteria, unless otherwise noted as preferred:

- Three (3) years of experience managing security assessment teams is
  required.

- Six (6) years of experience performing detailed, full-scope technical
  security control testing for each of the component types listed in
  Section C.3.1, including development of security and privacy
  assessment plans is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with the use of eGRC tools is required.
  Experience with ServiceNow is preferred.

- Experience working with Qualys Enterprise, Archer, Nessus, HCL
  AppScan, and technical outputs of the Kali Linux suite is required.

- Advanced knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Certified Information Systems Security Professional (CISSP), Certified
  Ethical Hacker (CEH), Certified Risk and Information Systems Control
  (CRISC), or Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Quality Assurance Lead

The Quality Assurance (QA) Lead shall meet the following minimum
criteria, unless otherwise noted as preferred:

- Three (3) years of managing technical security QA team/SA&A Package
  Independent Validation & Verification (IV&V) is required.

- Six (6) years of experience developing RMF documentation is required.

- Six (6) years of experience conducting security and privacy control
  assessments is required.

- Eight (8) years of Information Security experience is required.

- Two (2) years of experience with eGRC tools is required. Experience
  with ServiceNow GRC tool suite, including CAM, is preferred.

- Certified in Certified Information Systems Security Professional
  (CISSP), Certified Risk and Information Systems Control (CRISC), or
  Certified Information Security Auditor (CISA) is required.

<Insert Resume>

Tier 2 eGRC Specialist

The Tier 2 eGRC Specialist shall meet the following minimum criteria,
unless otherwise noted as preferred:

- At least two (2) years of experience with developing/writing advanced
  custom ServiceNow script includes, user interface actions, user
  interface policies, access control lists, client scripts, scheduled
  jobs, data tables and data fields in the last three years is required.

- At least three (3) years of Integrated Risk Management (IRM)/GRC
  implementation experience in the last four years is required; GRC CAM
  experience is preferred. ServiceNow Certified Risk & Compliance
  implementation specialist is preferred.

- At least (2) years of experience configuring and customizing the ITSM
  suite, IT Operations Management suite, and NOW Platform Capabilities
  in the last three years is required.

- At least (2) years of experience with technical components such as
  LDAP, Web Services, REST, SOAP, APIs, XML, JavaScript in the last
  three years is required.

- At least (2) years of experience with ITILv3 Service Management
  processes in the last three years is required.

- At least three (3) years of Administrator/Developer experience in the
  last four years is required; in addition, GRC ServiceNow Certified
  Developer is preferred.

- At least two (2) years of ServiceNow Implementation Specialist
  experience in the last three years is required; in addition, Certified
  ServiceNow Implementation Specialist is preferred.

<Insert Resume>

Automated Assessment Developer

The Automated Assessment Developer shall meet the following minimum
criteria:

- Five (5) years of experience as a software developer is required,

- Three (3) years of experience integrating cybersecurity tools,
  including Qualys, RSA Archer, and Splunk with ServiceNow GRC and
  ServiceNow formats and languages listed in Task C.2.7 is preferred.

- Bachelor of Science degree in Computer Science is required.

<Insert Resume>

IT Security Subject Matter Expert

The IT Security Subject-Matter Expert (SME), who handles areas requiring
elevated technical security skills (e.g., risk analysis of cutting-edge
technology, or high visibility project that requires a SME review due to
quick turn around with accurate results, etc.), and technical escalation
concerns that may arise in the implementation of new IT security and/or
compliance program requirements, security assessments, technical SA&A
package evaluations, etc., shall meet the following minimum criteria,
unless otherwise noted as preferred:

- Ten (10) years of IT security experience is required.

- Five (5) years of experience performing detailed, full-scope technical
  control testing for the component types listed in Section C.3.1 of
  this SOW including development security assessment plans is required.

- In-depth knowledge of and experience in implementing and using GRC
  platforms is required. Experience with ServiceNow GRC tool suite,
  including CAM preferred.

- Hands-on experience working with at least four (4) of the following
  seven (7) technology products: Forescout CounterAct, ArcSight, HCL
  BigFix, Sailpoint, CyberArk, RES, and Splunk is required.

- Extensive knowledge of the security configurations for the component
  types listed in Section C.3.1 is required.

- Bachelor of Science degree in Computer Science, Cybersecurity, or
  Information Systems. is required.

- Certified Information Systems Security Professional (CISSP) is
  required.

- Certified Cloud Security Professional (CCSP) is preferred.

<Insert Resume>

Past Performance

The Quoter shall request that references fill out and return three (3)
Past Performance Questionnaire Survey (Attachment 3). Quoters may not
submit the completed survey forms to the OCC Contract Specialist on
behalf of their references; instead, the completed forms shall be
returned directly by the referenced customers to the OCC Contract
Specialist, per the information provided on the form. The forms are due
to the OCC no later than the specified deadline for submitting
quotations in response to this RFQ. The OCC may also obtain and consider
any other relevant information in evaluating past performance, including
information contained in Contractor Performance Assessment Reporting
System (CPAR).
  
- Section Outline: Past Performance  
- RFP Summary: 
Proposal Info
•  Solicitation Number: RFQ 2031JW22Q00022 [Page 17]
•  NAICS Code: 541519 [Page 51]
•  Name/Title: Cybersecurity Assessment and Compliance (CA&C) Support [Page 1]
•  Solicitation Type: RFQ [Page 17]
•  Department/Agency Name: Comptroller of the Currency [Page 1]
•  Inquiries/Questions Due Date and Time: 12:00 PM (EST), October 12, 2021 [Page
51]
•  Proposal Due Date and Time: 10:00 AM (EST), October 29, 2021 [Page 50]
•  Mode of Submission: eBuy [Page 50]
•  Place of Performance: Online [Page 50]
•  Point of Contact (POC):
○  Primary: <EMAIL> [Page 51]
○  Secondary: <EMAIL> [Page 51]
•  Set Aside: Small Business [Page 1]
•  Period of Performance:
○  Base Period: 12 months, 01/03/2022 to 01/02/2023 [Page 2]
○  Option Period 1: 12 months, 01/03/2023 to 01/02/2024 [Page 3]
○  Option Period 2: 12 months, 01/03/2024 to 01/02/2025 [Page 4]
○  Option Period 3: 12 months, 01/03/2025 to 01/02/2026 [Page 5]
○  Option Period 4: 12 months, 01/03/2026 to 01/02/2027 [Page 5]
•  Key Personnel:
○  Project Manager [Page 22]
○  Cybersecurity Assurance and Compliance Program Support Lead [Page 22]
○  Security and Privacy Control Assessor Lead [Page 23]
○  Quality Assurance Lead [Page 24]
○  Tier 2 eGRC Specialist [Page 23]
○  Automated Assessment Developer [Page 23]
○  IT Security Subject Matter Expert [Page 23]
•  Security Clearance Requirements: N/A [Page 49]
•  Task Order Type: N/A [Page 50]
Purpose
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). [Section II - Statement of Work (SOW)] The CA&C
Program includes agency-level oversight for compliance with the Risk Management
Framework (RMF), Audit Liaison, and execution of Assessment and Authorization steps of the
RMF. The scope of work shall comprise: Project Management and Reporting; Cyber
Assurance and Compliance (CA&C) Program Support, including, Audit Liaison, Compliance
Oversight activities, CA&C program security documentation, and technical support for
automation and program tools; Security & Privacy Control Assessments of new and existing
OCC systems; Quality Assurance; and Administrative and IT Security Subject Matter Expert
Support. [Section II - Statement of Work (SOW)]
Scope
The purpose of this requirement is to provide comprehensive support to the Oﬃce of the
Comptroller of the Currency (OCC) Cybersecurity Assurance & Compliance (CA&C) Program
within the Cyber Security Oﬃce (CSO). The CA&C Program includes agency-level oversight
for compliance with the Risk Management Framework (RMF), Audit Liaison, and execution of
Assessment and Authorization steps of the RMF. The scope of work shall comprise: Project
Management and Reporting; Cyber Assurance and Compliance (CA&C) Program Support,
including, Audit Liaison, Compliance Oversight activities, CA&C program security
documentation, and technical support for automation and program tools; Security & Privacy
Control Assessments of new and existing OCC systems; Quality Assurance; and
Administrative and IT Security Subject Matter Expert Support.
Task Areas Overview
•  C.1 Project Management and Reporting
•  C.2 Cyber Assurance and Compliance Program Support
•  C.3 Security and Privacy Control Assessments
•  C.4 Quality Assurance
•  C.5 Administrative Support
•  C.6 IT Security Subject Matter Expert Support

  
- Knowledge Base: +---------------------------------------------------------------------------------------------------------------------------+
| ABC Government Solutions, Inc                                                                                             |
+==================+==============+==============+==============+==============+==============+==============+==============+
| Contracting Agency / Business   | Department of Veterans Affairs                                                          |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Applicable Contract Number      |                             | Total Dollar Value          |                             |
+---------------------------------+-----------------------------+-----------------------------+-----------------------------+
| Contract Name                   | FSS Program Management Support (HMS Technologies)                                       |
+---------------------------------+-----------------------------+--------------------------------------------+--------------+
| Contract Type                   | FFP                         | Place of Performance                       | Remote       |
+------------------+--------------+--------------+--------------+--------------+-----------------------------+--------------+
| Prime or         | Subcontractor               | Period of Performance       |                                            |
| Subcontractor    |                             |                             |                                            |
+------------------+-----------------------------+-----------------------------+--------------------------------------------+
| POC              |                                                                                                        |
+------------------+--------------------------------------------------------------------------------------------------------+
| Description of Services                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - ABC provides project oversight to VA OIS FSS (now ITOPS ESO) by prioritizing, planning, tracking, and reporting in      |
|   order to ensure implementation of OIS FSS projects, SharePoint Support, and Governance, including those FSS             |
|   requirements managed by the OIS Business Office support program. GGS provides support on the Continuous Readiness       |
|   Information Security Protection (CRISP) project.                                                                        |
|                                                                                                                           |
| - We develop and maintain an updated FSS Master Project Schedule and timeline for delivery of key components, milestones  |
|   and deliverables for ESO projects. We Provide visibility i and reporting on, the status and progress of activities      |
|   associated with CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones    |
|   (POA&M) remediation, action items, and standardization of training. Provide Action Item Update Reports, which track     |
|   progress status of VA wide tasks.                                                                                       |
+---------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                   |
+---------------------------------------------------------------------------------------------------------------------------+
| - FSS SHAREPOINT SUPPORT                                                                                                  |
|                                                                                                                           |
| ABC conducted analysis of information in the SharePoint workload tracker to identify and prioritize areas for             |
| improvements in FSS business operations methods to achieve improvements in FSS business operations which included:        |
|                                                                                                                           |
| 1.  Business process or procedures changes                                                                                |
|                                                                                                                           |
| 2.  Application of pertinent Technology (e.g., SharePoint tracking)                                                       |
|                                                                                                                           |
| 3.  Ability to utilize SharePoint applications to improve business operations                                             |
|                                                                                                                           |
| We Upgraded and enhanced the ISO Support Request Program (currently a MS Excel spreadsheet and PowerPoint presentation)   |
| that was a staff resource allocation program that managed the multitude of project/program requests for ISO support. As   |
| part of this task, we provided the customer the ability to lookup the ISO by name and facility, projects assigned, with   |
| links to other data sources, such as Governance, Risk, and Compliance (GRC), VA Systems Inventory (VASI), and other       |
| similar sources. Also, ABC delivered Tracking and Reporting SharePoint Pages for Government approval.                     |
|                                                                                                                           |
| - FSS CRISP PROGRAM SUPPORT                                                                                               |
|                                                                                                                           |
| ABC developed and then maintained an updated FSS Master Project Schedule and timeline for delivery of key components,     |
| milestones and deliverables for the CRISP project. We utilized MS Project to maintain the FSS Master Project Schedule of  |
| CRISP tasks, and all associated sub elements, producing static PDF views, and PowerPoint slides to document milestones.   |
|                                                                                                                           |
| We Coordinated project activities amongst regional directors, Network ISOs (NISOs), and other Subject Matter Experts      |
| (SMEs) related to CRISP. We provided visibility , and reporting on, the status and progress of activities associated with |
| CRISP sustainment, focusing on existing security practices, as well as Plan of Action and Milestones (POA&M) remediation, |
| action items, and standardization of training. We also provided Action Item Update Reports, which tracks progress         |
| upgrades, with countdown to 100 percent compliance as a part of the Monthly Status and Progress Report. We provided       |
| weekly communication utilizing existing VA virtual technologies (MS Lync, Veterans Affairs National Telecommunications    |
| System (VANTS) audio bridge, as examples, to FSS leadership as coordinated through the COR/FSS VA PM on status, issues,   |
| risks and accomplishments associated with FSS CRISP sustainment.                                                          |
|                                                                                                                           |
| We Provided Ad-hoc communications utilizing existing VA project management tools (MS Project, SharePoint, MS Excel, MS    |
| Word, and Adobe Pro) as necessary to meet FSS goals to project leads and OIS/OI&T leadership on status, issues, risks,    |
| and accomplishments associated with FSS CRISP sustainment. We also utilized existing project management tools (MS         |
| Project, SharePoint, MS Excel, MS Word, and Adobe Pro) and techniques to enhance visibility and escalate risks/issues     |
| associated with the project to FSS Director and other FSS management staff as coordinated through the COR/FSS VA PM. All  |
| weekly or ad-hoc communication work products were delivered, reviewed, and accepted by the COR/FSS VA PM prior to any     |
| distribution.                                                                                                             |
|                                                                                                                           |
| - GOVERNANCE SUPPORT                                                                                                      |
|                                                                                                                           |
| ABC provided ongoing program governance support to the FSS structured steering committee and governance approach, to      |
| include assessing and recommending the prioritization of projects and operational tasks to allow for better demand and    |
| workload management. We assisted in creating an FSS program strategy, mission, and goals in 1, 2, and 3 year increments,  |
| delivered as a MS Word document with an accompanying MS Project Plan and worked with FSS leadership to evaluate and       |
| potentially enhance existing strategy, mission, goals, and performance metrics. We monitored performance metrics which    |
| measure implementation in a graphical format (e.g. Dashboard, PowerPoint, and other concise formats). Performance metrics |
| were maintained and continuously revised, by us, as the need for change arises. We provided project governance management |
| expertise to support FSS Management and to ensure that all resulting projects and priorities shall align, and supported   |
| the mission and goals of FSS and worked with FSS Management to develop the mission and objectives of the organization,    |
| and present it to FSS leadership for review and approval.                                                                 |
|                                                                                                                           |
| As part of this ongoing support, ABC required, developed and maintained supporting templates, processes, metrics, and     |
| facilitates training sessions to ensure that programs are evaluated properly, have realistic timelines, and are aligned   |
| with FSS mission and goals.                                                                                               |
|                                                                                                                           |
| - FSS REQUIREMENTS ELABORATION PLANNING SUPPORT                                                                           |
|                                                                                                                           |
| As part of the requirement elaboration planning support, ABC assisted in updating the existing annual FSS Requirements    |
| Plan with the latest plan, delivery, and execution information for the current fiscal year. The FSS Requirements Plan     |
| includes a schedule that satisfies the needs of the identified stakeholders, as well as complements overarching VA and    |
| OI&T plans. Throughout the performance period, we assisted in maintaining the plan through quarterly ensuring the FSS     |
| Requirements Plan includes any updates in the area of identification of OIS stakeholders and their relevant interest in   |
| the program. We ensured the FSS Requirements Plan is maintained to include implementation, branding development, and      |
| channel development specific to FSS and collaborated with the FSS Director and management to manage, monitor and          |
| communicate FSS program status and goals. Prior to implementation of any areas of the plan, we gained acceptance and      |
| approval from the COR/FSS VA PM.                                                                                          |
|                                                                                                                           |
| We also assisted in coordinating the Weekly status meeting of the Bi-Weekly Activity Report (BWAR) deliverables with all  |
| FSS Management and Contractors. We created the BWAR using documents that detail current accomplishments/future            |
| activities, risks, and projected completion dates of each task. The BWAR was provided in ABC’s format. We provided        |
| communication support to include execution of the FSS Master Schedule, Timeline, and Milestone.                           |
|                                                                                                                           |
| - FSS Requirements Plan Execution: Assisted FSS management in overseeing requirements executed against the Plan, which    |
|   includes developing: e-mails, memorandums, briefings, presentations, bulletins, fact sheets, FSS Portal/Internet        |
|   postings, and posters and reached out to the stakeholders to determine informational requirements and shall tailor      |
|   these correspondence/communication products as needed.                                                                  |
|                                                                                                                           |
| - Medical Cyber Domain Support: Provided FSS ongoing communications and training support to ensure FSS stakeholders       |
|   understand threats to networked medical devices and patient care, mitigation strategies, and roles and responsibilities |
|   in implementing medical/special purpose device security.                                                                |
|                                                                                                                           |
| - FSS Incentive Program (FSSIP) Campaign Support: Collaborated with FSS leadership and designees to suggest improvements  |
|   for an effective FSSIP campaign. Working with FSS Leadership, we provided ideas, suggestions, and technical assistance  |
|   to plan and update the FSSIP information through all modalities including: FSSIP on the FSS Portal; email distribution; |
|   FSS Bulletins; Field Security Service Director (FSSD) monthly updates; IS Blog, OI&T Blogs and Newsletters, and         |
|   Vanguard Magazine.                                                                                                      |
|                                                                                                                           |
| - FSS Communications Advisory Board (CAB) Support: On a monthly basis, ABC collaborated with the CAB Chair and FSS        |
|   Director to select meeting topics. We facilitated in-house meetings, provide briefings of activities from last meeting, |
|   support topic research, capture notes, poll the group on topics, and conduct live meetings.                             |
|                                                                                                                           |
| - FSS Executive Requirements And Ad Hoc Support: Provided assist in rapid-response communications on priority security    |
|   matters such as: leadership messages to the field, presentations, talking points, executive memorandums, bulletins, and |
|   ad hoc training on new security initiatives.                                                                            |
+---------------------------------------------------------------------------------------------------------------------------+

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC                                                                                                                                                                     |
+================+================+================+================+================+================+================+================+================+================+
| Contracting Agency / Business                    | Department of Veterans Affairs                                                                                       |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Applicable Contract Number                       |                                                  | Total Dollar Value                               |                |
+--------------------------------------------------+--------------------------------------------------+--------------------------------------------------+----------------+
| Contract Name                                    | Security Control Assessments                                                                                         |
+----------------+---------------------------------+---------------------------------+--------------------------------------------------+---------------------------------+
| Contract Type  | Firm Fixed Price (FFP)                                            | Place of Performance                             | Remote Locations                |
+----------------+----------------+---------------------------------+----------------+---------------------------------+----------------+---------------------------------+
| Prime or Subcontractor          |                                 | Period of Performance                            | 12 months from date of award, with four 12-month |
|                                 |                                 |                                                  | option periods.                                  |
+---------------------------------+---------------------------------+--------------------------------------------------+--------------------------------------------------+
| POC                             |                                                                                                                                       |
+---------------------------------+---------------------------------------------------------------------------------------------------------------------------------------+
| Background                                                                                                                                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| The mission of the Department of Veterans Affairs (VA), Office of Information & Technology (OI&T), Office of Information Security (OIS) is to provide benefits and      |
| services to Veterans of the United States. In meeting these goals, OI&T strives to provide high quality, effective, and efficient Information Technology (IT) services  |
| to those responsible for providing care to Veterans at the point-of-care as well as throughout all the points of Veterans’ health care in an effective, timely and      |
| compassionate manner. VA depends on Information Management/Information Technology (IM/IT) systems to meet mission goals.                                                |
|                                                                                                                                                                         |
| During the annual Federal Information Systems Controls Audit Manual (FISCAM) audits and Consolidated Financial Statement Audit (CFSA), the VA Office of Inspector       |
| General (OIG) has determined there is an IT Security Controls Material Weakness. These controls are in such areas as security management, access controls,              |
| configuration management, and contingency planning, and include significant technical weaknesses in databases, servers, and network devices, such as previously         |
| identified and unpatched vulnerabilities. This project ensures that the continued compliance and oversight of the security control objectives are met to support        |
| Federal regulatory requirements and the Federal Government oversight and audit community.                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Scope of Work                                                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ABC performed:                                                                                                                                                          |
|                                                                                                                                                                         |
| 1.  Assess Security Controls: Determined the extent to which required NIST information security controls (reference Section 2.0 Applicable Documents) are implemented   |
|     correctly in VA’s information systems that are contained within VA’s accreditation boundaries, operating as intended, and producing the desired outcome in meeting  |
|     security requirements.                                                                                                                                              |
|                                                                                                                                                                         |
| a)  Develop and document the Security Assessment Plan (SAP) that details the scope of the test, the controls to be tested, and the execution method to conduct the      |
|     test.                                                                                                                                                               |
|                                                                                                                                                                         |
| b)  Execute the SAP.                                                                                                                                                    |
|                                                                                                                                                                         |
| c)  Prepare the security assessment report documenting the issues, findings, and recommendations from the Security Controls Assessment (SCA).                           |
|                                                                                                                                                                         |
| 2.  Assess a selected subset, as detailed in the SAP consistent with NIST guidelines, of the technical, management, and operational security controls employed within   |
|     or inherited by VA’s information systems (accreditation boundary) in accordance with the organization-defined monitoring strategy to determine the overall          |
|     effectiveness of the controls (i.e., the extent to which the controls are implemented correctly, operating as intended, and producing the desired outcome with      |
|     respect to meeting the security requirements for the system). ABC also provided an assessment of the severity of weaknesses or deficiencies discovered in the       |
|     information systems and its environment of operation and recommend corrective actions to address identified vulnerabilities. In addition to the above               |
|     responsibilities, Contractor Security Controls Assessors prepare the final security assessment report containing the results and findings from the assessment.      |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Specific Task Relevance                                                                                                                                                 |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Contractor Independent Assessment                                                                                                                                       |
|                                                                                                                                                                         |
| ABC performed SCAs for VA’s information systems that are contained within VA’s accreditation boundaries (approximately 375 VA information systems). VA’s information    |
| systems consist of 171 FIPS High, 115 FIPS Moderate and up to 75 additional systems yet be classified but expected to be at the High or Moderate impact level. Because  |
| SCAs expire every three years, it is estimated that one third of VA’s information systems, approximately 125, need to be assessed each year. We also provided an        |
| assessment of the criticality of each weakness or deficiency found based on prioritized levels reported by NIST discovered in the information systems and their         |
| environment of operation and recommend corrective actions to address identified vulnerabilities.                                                                        |
|                                                                                                                                                                         |
| ABC provided auditable methodologies to ensure all SCA tests are developed, executed, and reported consistent with all VA Policy, VA’s Assessment and Authorization     |
| (A&A) Standard Operating Procedure (SOP), and the SCA Guidelines. We ensured SCAs are conducted in accordance with VA Policy and the Risk Management Framework (RMF).   |
|                                                                                                                                                                         |
| ABC performed the following preparation, assessment, reporting, and continuous monitoring for each system being assessed.                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment Preparation                                                                                                                                 |
|                                                                                                                                                                         |
| - ABC reviewed all required supporting artifacts of record in the Governance, Risk, and Compliance tool as identified in both the A&A SOP and the security requirements |
|   captured in the NIST RMF, to support the security state of the system being assessed. Upon review, our team collaborated with the System Owner (SO) and SO staff to   |
|   develop a System Rules of Engagement (ROE) Agreement.                                                                                                                 |
|                                                                                                                                                                         |
| The ROE must correctly identify the following:                                                                                                                          |
|                                                                                                                                                                         |
| 1)  Scope of testing                                                                                                                                                    |
|                                                                                                                                                                         |
| a)  ABC only tested for controls within scope of system type, location, and within the appropriate management control                                                   |
|                                                                                                                                                                         |
| 2)  Network ranges being assessed                                                                                                                                       |
|                                                                                                                                                                         |
| 3)  System components being assessed                                                                                                                                    |
|                                                                                                                                                                         |
| 4)  Locations being assessed                                                                                                                                            |
|                                                                                                                                                                         |
| 5)  SCA and all members conducting assessments including systems being used                                                                                             |
|                                                                                                                                                                         |
| 6)  Assessment type and method                                                                                                                                          |
|                                                                                                                                                                         |
| 7)  Tools used for the assessment                                                                                                                                       |
|                                                                                                                                                                         |
| 8)  A detailed list of artifacts or evidence not of record with the system and needed to support the SCA                                                                |
|                                                                                                                                                                         |
| - ABC developed and submitted a SCA Test Plan which:                                                                                                                    |
|                                                                                                                                                                         |
| 1.  Identified and documented the appropriate security assessment level of effort and project management information to include tasks, reviews (including compliance    |
|     reviews), resources, and milestones for the system being tested.                                                                                                    |
|                                                                                                                                                                         |
| 2.  Listed key resources necessary to complete the SCA, including the Government staff needed onsite or remotely to satisfy the SCA activities.                         |
|                                                                                                                                                                         |
| 3.  Listed key roles and personnel participating in security assessment activities with an anticipated schedule of availability to support the SCA.                     |
|                                                                                                                                                                         |
| 4.  Identified the controls to be tested, the current status of the controls, and provide the test procedures to be used as part of the SCA. The test procedures were   |
|     used from the OCS Test Bank and updated, where appropriate, to reflect current system conditions.                                                                   |
|                                                                                                                                                                         |
| 5.  Included an overall assessment process flow or swim-lane diagram which documents the steps required to conduct assessment activities and interact with all          |
|     necessary parties.                                                                                                                                                  |
|                                                                                                                                                                         |
| - ABC developed and documented a RMF SAP and perform the SCA according to the SAP. The SAP included a complete and comprehensive description of all processes and       |
|   procedures our team performed. The Government provided the list of systems that require testing and access to resources that support the testing of the systems       |
|   needed to accomplish the SCA as defined in the SAP. The list of security controls/enhancements and the SAP were taken into account information systems’ security      |
|   categorization.                                                                                                                                                       |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Control Assessment                                                                                                                                             |
|                                                                                                                                                                         |
| - ABC performed the SCA according to the processes and procedures described in the SAP.                                                                                 |
|                                                                                                                                                                         |
| - ABC completed the following communication and reporting activities:                                                                                                   |
|                                                                                                                                                                         |
| 1.  SCA Pre-Site Meeting                                                                                                                                                |
|                                                                                                                                                                         |
| 2.  System Component Assessment Daily Status                                                                                                                            |
|                                                                                                                                                                         |
| 3.  Weekly Status Report                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  SCA Out-Brief Meeting                                                                                                                                               |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security Assessment Report                                                                                                                                              |
|                                                                                                                                                                         |
| - ABC developed the Security Assessment Report to include the following:                                                                                                |
|                                                                                                                                                                         |
| 1.  Documentation of each SCA                                                                                                                                           |
|                                                                                                                                                                         |
| 2.  Assessment test objectives as identified in NIST SP 800-53A                                                                                                         |
|                                                                                                                                                                         |
| 3.  Assessment test types (e.g., interview, examine, test) as identified in NIST SP 800-53A                                                                             |
|                                                                                                                                                                         |
| 4.  All software and hardware components assessed                                                                                                                       |
|                                                                                                                                                                         |
| 5.  Sequential, step-by-step assessment procedures for testing each test objective                                                                                      |
|                                                                                                                                                                         |
| 6.  Results of control assessment, evaluation, and analysis of the system within the defined system boundary, supporting infrastructure, and operating environment      |
|                                                                                                                                                                         |
| 7.  Evidence that all components in the system inventory were tested or covered by a test performed on a representative sample of identically configured devices        |
|                                                                                                                                                                         |
| 8.  Rationale for any system or device in the inventory not directly tested                                                                                             |
|                                                                                                                                                                         |
| 9.  Results that ensure configuration settings for all major IT products in the system were assessed, identifying each system component, secure benchmark assessed,     |
|     location of scan results, confirmation the assessed component implements approved organizational, defined, secure benchmark                                         |
|                                                                                                                                                                         |
| 10. Determination that the security control is “Satisfied” or “Other Than Satisfied” with each sequential step of the assessment process providing a “Satisfied” or     |
|     “Other Than Satisfied” determination                                                                                                                                |
|                                                                                                                                                                         |
| 11. Results of Findings to be documented in the OCS POA&M Import Template (data                                                                                         |
|                                                                                                                                                                         |
| defined file in Excel format)                                                                                                                                           |
|                                                                                                                                                                         |
| 12. Actual, unbiased, and factual results and analysis used to make final determinations that the security control is “Satisfied” or “Other Than Satisfied” with actual |
|     results for each system component type                                                                                                                              |
|                                                                                                                                                                         |
| 13. Identification and explanation for all artifacts used in the assessment, as generated or provided by the SO                                                         |
|                                                                                                                                                                         |
| - ABC provided all Assessment Documentation developed to support assessment, artifact collection, findings, analysis, conclusions, management recommendations, and      |
|   reports (documentation and reports required by the Government, SCA developed, or a combination of Government required and SCA developed)                              |
|                                                                                                                                                                         |
| - ABC developed and updated Lessons Learned from A&A activities and incorporated these into processes and procedures as applicable. Approval must be gained from the VA |
|   Project Manager on recommendations resulting from Lessons Learned before they are incorporated into existing processes and procedures. Feedback on Lessons Learned    |
|   were collected from the following individuals:                                                                                                                        |
|                                                                                                                                                                         |
| 1.  AO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 2.  SO                                                                                                                                                                  |
|                                                                                                                                                                         |
| 3.  ISSO                                                                                                                                                                |
|                                                                                                                                                                         |
| 4.  IT System Manager/System Steward                                                                                                                                    |
|                                                                                                                                                                         |
| 5.  Security Controls Assessor                                                                                                                                          |
|                                                                                                                                                                         |
| 6.  VA Program Manager                                                                                                                                                  |
|                                                                                                                                                                         |
| 7.  COR                                                                                                                                                                 |
|                                                                                                                                                                         |
| 8.  CO                                                                                                                                                                  |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Ongoing Security Control Assessments                                                                                                                                    |
|                                                                                                                                                                         |
| - ABC developed a Continuous Monitoring SCA Plan and Schedule. This plan should include required activities and outputs required by RMF Tasks                           |
|                                                                                                                                                                         |
| - We performed Continuous Monitoring Annual SCAs according the Continuous Monitoring SCA Plan and Schedule.                                                             |
|                                                                                                                                                                         |
| - We performed all required communications and reporting activities as required by RMF Tasks                                                                            |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


