import os
import io
import logging
import subprocess
from typing import List

import fitz
import pikepdf
from pypdf import PdfReader

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_documents_content(paths: List[str]) -> str:
    """
    Extract and combine content from PDF and DOCX files given a list of file or directory paths.

    Args:
        paths (List[str]): List of file or directory paths to search for documents

    Returns:
        str: Combined content from all PDF and DOCX files

    Raises:
        FileNotFoundError: If a path doesn't exist
        Exception: For document processing errors
    """
    combined_content = []

    for path in paths:
        if not os.path.exists(path):
            raise FileNotFoundError(f"Path not found: {path}")

        # If the path is a file, process it directly.
        if os.path.isfile(path):
            logger.info(f"Processing file at {path}")
            file_extension = os.path.splitext(path)[1].lower()
            try:
                if file_extension == '.pdf':
                    logger.info("The file is a PDF.")
                    content = extract_pdf_content(path)
                    if content:
                        combined_content.append(f"\n{content}\n")
                    else:
                        logger.warning(f"No content extracted from {path}")

                elif file_extension == '.docx':
                    logger.info("The file is a DOCX.")
                    # This now calls the updated pandoc-based function
                    content = extract_docx_content(path)
                    if content:
                        combined_content.append(content)
                        logger.info(f"Extracted {len(content)} characters from DOCX file using Pandoc")
                    else:
                        logger.warning(f"No content extracted from {path} using Pandoc")
                else:
                    logger.warning(f"Unsupported file type for path: {path}")

            except Exception as e:
                logger.error(f"Error processing {path}: {str(e)}")
                continue

        # If the path is a directory, walk through it recursively.
        elif os.path.isdir(path):
            for root, _, files in os.walk(path):
                for file in files:
                    file_path = os.path.join(root, file)
                    logger.info(f"Processing file at {file_path}")
                    file_extension = os.path.splitext(file)[1].lower()
                    try:
                        if file_extension == '.pdf':
                            logger.info("The file is a PDF.")
                            content = extract_pdf_content(file_path)
                            if content:
                                combined_content.append(f"\n{content}\n")
                            else:
                                logger.warning(f"No content extracted from {file_path}")

                        elif file_extension == '.docx':
                            logger.info("The file is a DOCX.")
                            # This now calls the updated pandoc-based function
                            content = extract_docx_content(file_path)
                            if content:
                                combined_content.append(content)
                                logger.info(f"Extracted {len(content)} characters from DOCX file using Pandoc")
                            else:
                                logger.warning(f"No content extracted from {file_path} using Pandoc")
                        else:
                            logger.debug(f"Skipping unsupported file type: {file_path}")
                    except Exception as e:
                        logger.error(f"Error processing {file_path}: {str(e)}")
                        continue

        else:
            logger.warning(f"Path is neither a file nor a directory: {path}")

    return '\n'.join(combined_content)

def extract_pdf_content(file_path: str) -> str:
    """
    Extract text from a PDF file using multiple libraries for robustness.
    
    Args:
        file_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text or empty string if all methods fail
    """
    # Method 1: Try PyMuPDF (fitz) first - generally fast and reliable
    try:
        doc = fitz.open(file_path)
        # Check for MuPDF warnings
        warnings = fitz.TOOLS.mupdf_warnings()
        if warnings:
            logger.warning(f"MuPDF warnings encountered: {warnings}")
        
        content = ""
        for page_num in range(doc.page_count):
            page = doc.load_page(page_num)
            content += page.get_text("text")
        doc.close()
        
        if content.strip():
            logger.info("Successfully extracted content with PyMuPDF")
            return content
        else:
            logger.warning("PyMuPDF returned empty content, trying other methods")
    except Exception as e:
        logger.warning(f"PyMuPDF extraction failed: {e}")
    
    # Method 2: Try pikepdf + pypdf - more robust with damaged PDFs
    try:
        logger.info("Attempting PDF repair with pikepdf")
        with pikepdf.open(file_path, allow_overwriting_input=False) as pdf:
            buffer = io.BytesIO()
            pdf.save(buffer)
            buffer.seek(0)
            
            reader = PdfReader(buffer)
            content = ""
            for page in reader.pages:
                content += page.extract_text() or ""
                
        if content.strip():
            logger.info("Successfully extracted content with pikepdf + pypdf")
            return content
        else:
            logger.warning("pikepdf + pypdf returned empty content")
    except Exception as e:
        logger.warning(f"pikepdf + pypdf extraction failed: {e}")
    
    # Method 3: Direct pypdf as fallback
    try:
        logger.info("Attempting direct pypdf extraction")
        reader = PdfReader(file_path, strict=False)
        content = ""
        for page in reader.pages:
            content += page.extract_text() or ""
            
        if content.strip():
            logger.info("Successfully extracted content with direct pypdf")
            return content
        else:
            logger.warning("Direct pypdf returned empty content")
    except Exception as e:
        logger.warning(f"Direct pypdf extraction failed: {e}")
    
    logger.error(f"All PDF extraction methods failed for {file_path}")
    return ""

def extract_docx_content(file_path: str) -> str:
    """
    Extract text from a DOCX file using pandoc.
    Pandoc typically extracts all textual content, including the Table of Contents,
    main body, headers, footers, footnotes, and tables.
    """
    logger.info(f"Attempting DOCX text extraction with pandoc for: {file_path}")
    try:
        # Command to convert docx to plain text using pandoc
        # -s: standalone (ensures proper handling of headers/footers)
        # -t plain: output format is plain text
        command = ['pandoc', '-s', file_path, '-t', 'plain']

        # Execute the pandoc command
        process = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',  # Explicitly set encoding to UTF-8
            errors='ignore',    # Ignore characters that still can't be decoded
            check=True
        )

        extracted_text = process.stdout
        logger.info(f"Successfully extracted text from '{file_path}' using pandoc.")
        
        return extracted_text

    except subprocess.CalledProcessError as e:
        logger.error(f"Pandoc error during extraction from '{file_path}': {e}")
        logger.error(f"Pandoc STDERR: {e.stderr}")
        return ""
    except FileNotFoundError:
        logger.error(
            "Pandoc command not found. Please ensure pandoc is installed and in your system's PATH."
        )
        return ""
    except Exception as e:
        logger.error(f"An unexpected error occurred with pandoc extraction for '{file_path}': {e}", exc_info=True)
        return ""

#=================This is actual==================================
import os

def create_markdown_file(filename: str, content: str):
    """Creates a new markdown file and adds content to it.
    
    Args:
        filename (str): Name of the file (should include .md extension).
        content (str): Content to be written to the file.
    
    Returns:
        str: Confirmation message with filename.
    """
    if not filename.endswith(".md"):
        filename += ".md"
    
    # Create directory if it doesn't exist
    directory = os.path.dirname(filename)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
    
    try:
        with open(filename, "w", encoding="utf-8") as file:
            file.write(content)
        return f"Markdown file '{filename}' created successfully."
    except Exception as e:
        return f"Error creating file: {e}"

def clean_markdown_code_block(text: str) -> str:

    # Remove leading whitespace or newlines
    text = text.strip()
    
    # Check if text starts with ```
    if text.startswith("```"):
        # Find the first newline to remove the entire first line (```language)
        first_newline = text.find('\n')
        if first_newline != -1:
            text = text[first_newline + 1:]
    
    # Remove ending ```
    if text.rstrip().endswith("```"):
        text = text[:text.rstrip().rfind("```")]
    
    return text.strip()

import json
import re
from typing import Any

def clean_json_code_block(text: str) -> Any:
    """
    Extracts JSON content from a markdown code block and returns the JSON object.
    The expected format is:
    
    ```json
    { ... valid json ... }
    ```
    
    Raises ValueError if no valid JSON code block is found.
    """
    # Use regex to match content between ```json and ```
    match = re.search(r'```json\s*(.*?)\s*```', text, re.DOTALL)
    if not match:
        raise ValueError("No JSON code block found in the provided text.")
    
    json_content = match.group(1)
    return json.loads(json_content)


outline_file = [r"D:\CONTENT_GEN\files\OneDrive_2025-02-20\Requirement Documents\Iram\working documents\Outline\Office of the Comptroller of the Currency_RFQ_2031JW22Q00022_Cybersecurity Assessment and Compliance_Volume 1 Outline.docx"]
# past_performance_file = [r"D:\CONTENT_GEN\files\OneDrive_2025-02-20\Requirement Documents\Adnan\Past Perrformance\Capability Statement_redacted (1).pdf"]

past_performance_file = [r"D:\Respone_gen_2\files\OneDrive_2025-02-20\Requirement Documents\Iram\working documents\Past Performance\FSS Program Management Support (HMS Technologies).docx",r"D:\Respone_gen_2\files\OneDrive_2025-02-20\Requirement Documents\Iram\working documents\Past Performance\Security Control Assessments.docx"]
summary_file = [r"D:\CONTENT_GEN\files\OneDrive_2025-02-20\Requirement Documents\Iram\Summary\Iram_Summary.pdf"]
generated_response_file_name = r"D:\CONTENT_GEN\Generic_Results\Iram1"



try:
    outline_content = extract_documents_content(outline_file)
    past_performance_content = extract_documents_content(past_performance_file)
    summary_content = extract_documents_content(summary_file)
    # past_performance_content = "No past performance content available."
    # past_performance_content = "No Past Performance"
    print("Successfully extracted content from all documents")
except Exception as e:
    print(f"Error: {str(e)}")

from IPython.display import Markdown


# Markdown(outline_content)

# Markdown(past_performance_content)

# Markdown(summary_content)

from langchain_google_vertexai import ChatVertexAI
from dotenv import load_dotenv
load_dotenv()

model = ChatVertexAI(model_name = "gemini-2.5-flash", temperature = 0.2)
#model = ChatVertexAI(model_name = "gemini-2.0-flash-thinking-exp-01-21", temperature = 0.7)
# model = ChatVertexAI(model_name = "gemini-2.5-flash-preview-04-17", temperature = 0.2)
# model = ChatVertexAI(model_name = "gemini-2.5-flash-preview-05-20", temperature = 0.2)
#model = ChatVertexAI(model_name = "gemini-2.5-pro-exp-03-25", temperature = 0.5)
# model = ChatVertexAI(model_name = "gemini-1.5-flash-001", temperature = 0.5)


load_dotenv()

from old_prompts2 import get_response_v6, get_outline_breakdown_prompt

outline_breakdown = model.invoke(get_outline_breakdown_prompt(outline_content))

jsonified_outline = clean_json_code_block(outline_breakdown.content)

jsonified_outline

import time
combined_response = ""

# Create or clear the log file before the loop starts
with open("new_adnann2_prompts_log.txt", "w", encoding="utf-8") as f:
    f.write("====================Prompt Log Start ================================================\n\n")

# Loop through each section in the sections array
for section in jsonified_outline["sections"]:
    section_number = section["section_number"]
    section_title = section["section_title"]
    
    # Extract all subsection titles for this section
    # subsection_titles = [subsection["subsection_title"] for subsection in section["subsections"]]
    
    print(f"====================Processing section {section_number}================")
    # print(subsection_titles)
    
    # Generate the prompt for this section, including all its subsection titles
    prompt = get_response_v6(outline_content, section_title, past_performance_content,summary_content)
    
        # Print prompt (what LLM will receive)
    print(f"\n======================== Prompt for Section {section_number}: {section_title} ====================\n")
    print(prompt)

    # Save prompt to file
    with open("new_adnann2_prompts_log.txt", "a", encoding="utf-8") as f:
        f.write(f"\n=================== Prompt for Section {section_number}: {section_title} ====================\n")
        f.write(prompt + "\n")

    # print("prompt",prompt)
    # Invoke the model with the generated prompt
    response = model.invoke(prompt)
    print(f"-------------------------RESPONSE for {section_number}:{section_title}---------------")
    print(response.content[:100])
    print(f"-------------------------END RESPONSE for {section_number}:{section_title}---------------")
    
    print(f"====================== END Section {section_number}==================================")
    # Clean the markdown code block from the response
    cleaned_response = f"START {section_number}:{section_title}\n\n" + clean_markdown_code_block(response.content)+ f"END {section_number}:{section_title}\n\n"    
    # Append the cleaned response to the combined string
    combined_response += cleaned_response + "\n\n"
    time.sleep(2)


print(section_title)

# Save the final combined response to a file
create_markdown_file(generated_response_file_name, combined_response)
# create_markdown_file("test/test.md", combined_response)
print(f"Successfully saved document to {generated_response_file_name}")

Markdown(combined_response)





# jsonified_outline["section_1"]

# #done by me
# for section in jsonified_outline["sections"]:
#     print(f"\nSection {section['section_number']}: {section['section_title']}")
#     for subsection in section["subsections"]:
#         print(f"   - {subsection['subsection_title']}")


# import time
# combined_response = ""

# # Loop through each section in the sections array
# for section in jsonified_outline["sections"]:
#     section_number = section["section_number"]
#     section_title = section["section_title"]
    
#     # Extract all subsection titles for this section
#     subsection_titles = [subsection["subsection_title"] for subsection in section["subsections"]]

#     print(f"\n==================== Processing Section {section_number}: {section_title} ====================")
    
#     # Print all inputs going into the prompt generator
#     print(f"\n--- Input to get_response_generation_prompt() for Section {section_number} ---")
#     print("Subsection Titles:")
#     for idx, title in enumerate(subsection_titles, 1):
#         print(f"  {idx}. {title}")
#     print("\nOutline Content:\n", outline_content[:500], "..." if len(outline_content) > 500 else "")
#     print("\nPast Performance Content:\n", past_performance_content[:500], "..." if len(past_performance_content) > 500 else "")
#     print("\nSummary Content:\n", summary_content[:500], "..." if len(summary_content) > 500 else "")
#     print("--------------------------------------------------------------------------\n")
    
#     # Generate the prompt for this section
#     prompt = get_response_generation_prompt(
#         outline_content, 
#         subsection_titles,
#         past_performance_content, 
#         summary_content
#     )

#     # Print the generated prompt
#     print(f"\n=== Generated Prompt for Section {section_number}: {section_title} ===")
#     print(prompt[:1000], "..." if len(prompt) > 1000 else "")  # Only show first 1000 chars for readability
#     print("====================================================================\n")
    
#     # Invoke the model with the generated prompt
#     response = model.invoke(prompt)
#     print(f"------------------------- RESPONSE for {section_number}: {section_title} -------------------------")
#     print(response.content[:100])  # Preview response
#     print(f"------------------------- END RESPONSE for {section_number}: {section_title} -------------------------")

#     # Clean and store response
#     cleaned_response = f"START {section_number}:{section_title}\n\n" + clean_markdown_code_block(response.content) + f"\n\nEND {section_number}:{section_title}\n\n"
#     combined_response += cleaned_response + "\n\n"
    
#     time.sleep(5)


# Markdown(combined_response)

# response = model.invoke(prompt)
# # response

# response = clean_markdown_code_block(response.content)

# create_markdown_file(generated_response_file_name, combined_response)

# import asyncio
# from concurrent.futures import ThreadPoolExecutor

# async def process_section(section, outline_content, past_performance_content, summary_content):
#     section_number = section["section_number"]
#     section_title = section["section_title"]
    
#     # Extract all subsection titles for this section
#     subsection_titles = [subsection["subsection_title"] for subsection in section["subsections"]]
    
#     print(f"====================Processing section {section_number}================")
    
#     # Generate the prompt for this section, including all its subsection titles
#     prompt = get_response_generation_prompt(
#         outline_content, 
#         subsection_titles,
#         past_performance_content, 
#         summary_content
#     )
    
#     # Invoke the model with the generated prompt - using a ThreadPoolExecutor because model.invoke is likely blocking
#     loop = asyncio.get_running_loop()
#     with ThreadPoolExecutor() as executor:
#         response = await loop.run_in_executor(executor, lambda: model.invoke(prompt))
    
#     print(f"-------------------------RESPONSE for {section_number}:{section_title}---------------")
#     print(response.content[:100])
#     print(f"-------------------------END RESPONSE for {section_number}:{section_title}---------------")
    
#     print(f"====================== END Section {section_number}==================================")
    
#     # Clean the markdown code block from the response
#     cleaned_response = clean_markdown_code_block(response.content)
    
#     return section_number, cleaned_response

# async def main_async():
#     combined_response = ""
    
#     # Create tasks for all sections
#     tasks = []
#     for section in jsonified_outline["sections"]:
#         task = asyncio.create_task(
#             process_section(section, outline_content, past_performance_content, summary_content)
#         )
#         tasks.append(task)
    
#     # Gather all results
#     results = await asyncio.gather(*tasks)
    
#     # Sort results by section number to maintain order
#     sorted_results = sorted(results, key=lambda x: x[0])
    
#     # Combine all responses in the correct order
#     combined_response = "\n\n".join([result[1] for result in sorted_results])
    
#     return combined_response

# import nest_asyncio
# nest_asyncio.apply()  # This allows for nested event loops

# # Then you can use asyncio.run() as before
# combined_response = asyncio.run(main_async())

# from crewai import Agent, Task, Crew, Process,    
# from textwrap import dedent
# import os
# from dotenv import load_dotenv
# load_dotenv()

# llm = LLM(
# model="vertex_ai/gemini-2.0-flash-thinking-exp-01-21", temperature=0.5)
# # model="vertex_ai/gemini-2.0-flash-001", temperature=0.6)

# from crewai import Agent, Task, Crew, Process


# # Define the agents based on the documentation

# requirements_analyzer = Agent(
#     llm=llm,
#     role='Procurement Requirements Analyzer',
#     goal='Extract and organize all requirements from procurement documents to guide response development',
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You are an expert in government and corporate procurement processes with deep experience in "
#         "analyzing RFP documents. You have a keen eye for identifying both explicit and implicit "
#         "requirements, along with evaluation criteria. Your skill in prioritizing requirements and "
#         "creating structured summaries is essential for successful bids."
#     ),
#     allow_delegation=True
# )

# past_performance_specialist = Agent(
#     llm=llm,
#     role='Past Performance Specialist',
#     goal='Identify the most relevant past performance examples that demonstrate capability to meet current requirements',
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You are highly skilled at matching an organization's past performances to current procurement "
#         "requirements. You excel at identifying projects with similar scale, complexity, or domain relevance, "
#         "and extracting quantifiable results that will resonate with evaluators. Your expertise helps "
#         "organizations showcase their proven capabilities in the most persuasive way."
#     ),
#     allow_delegation=True
# )

# response_writer = Agent(
#     llm=llm,
#     role='Proposal Response Writer',
#     goal='Create compelling, requirement-focused proposal content that persuasively addresses all requirements',
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You are a talented writer specialized in crafting winning procurement responses. You excel at "
#         "creating content that directly addresses requirements while highlighting your organization's "
#         "capabilities and competitive advantages. Your writing is clear, persuasive, and strategically "
#         "focused on evaluation criteria."
#     ),
#     allow_delegation=True
# )

# compliance_checker = Agent(
#     llm=llm,
#     role='Compliance Verification Specialist',
#     goal='Ensure all responses meet RFP compliance requirements and address evaluation criteria',
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You are a detail-oriented compliance expert who ensures proposals meet all stated requirements. "
#         "You methodically verify that every requirement is addressed, all evaluation criteria are supported "
#         "with evidence, and formatting requirements are followed. Your thorough reviews prevent "
#         "disqualification and maximize evaluation scores."
#     ),
#     allow_delegation=True
# )

# sme_coordinator = Agent(
#     llm=llm,
#     role='Subject Matter Expert Coordinator',
#     goal='Identify areas requiring specialized input and formulate specific questions for SMEs',
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You excel at identifying technical gaps in proposal responses and formulating precise questions "
#         "for subject matter experts. You understand complex technical requirements and know how to extract "
#         "the exact information needed from specialists to create winning proposals. Your coordination "
#         "ensures all technical aspects of a response are accurate and compelling."
#     ),
#     allow_delegation=True
# )

# # Define the tasks based on the workflow

# analyze_requirements_task = Task(
#     description=(
#         "Review the entire procurement outline and extract all requirements, priorities, and evaluation criteria. "
#         "Create a structured summary to guide response development.\n\n"
#         "Input procurement outline: {outline_content}"
#     ),
#     expected_output='A comprehensive requirements analysis document that extracts and organizes all requirements, priorities, evaluation criteria, and compliance needs from the procurement documents.',
#     agent=requirements_analyzer
# )

# find_relevant_performances_task = Task(
#     description=(
#         "Review the requirements analysis and available past performance data. Match past performances to specific "
#         "requirements and select the 3-5 most relevant examples. Document why each example is relevant.\n\n"
#         "Past performance data: {past_performance_content}"
#     ),
#     expected_output='A summary of the 3-5 most relevant past performances with clear rationale explaining why each example demonstrates capability to meet the current requirements.',
#     agent=past_performance_specialist
# )

# identify_sme_needs_task = Task(
#     description=(
#         "Review the requirements analysis and identify technical or specialized requirements that need SME input. "
#         "Formulate specific questions for subject matter experts, prioritize them, and organize by topic area.\n\n"
#     ),
#     expected_output='A structured list of specific, actionable questions for SMEs, organized by topic area and prioritized by importance.',
#     agent=sme_coordinator
# )

# write_initial_response_task = Task(
#     description=(
#         "Create the first draft of the procurement response based on the requirements analysis, past performances, "
#         "and SME input. Draft a comprehensive response addressing all requirements, incorporate past performances "
#         "as evidence, and follow the structure outlined in the procurement document.\n\n"
#         "SME input needs: no SME input available"
#     ),
#     expected_output='A complete draft response document that addresses all requirements, incorporates relevant past performances, and follows the required structure and format.',
#     agent=response_writer
# )

# verify_compliance_task = Task(
#     description=(
#         "Review the draft response against the requirements analysis to verify all requirements are addressed "
#         "properly, all evaluation criteria are supported with evidence, and the response complies with format "
#         "and structure requirements. Create a report identifying any gaps or issues.\n\n"
#     ),
#     expected_output='A compliance verification report that identifies any gaps, weaknesses, or compliance issues in the draft response, with specific recommendations for improvement.',
#     agent=compliance_checker
# )

# finalize_response_task = Task(
#     description=(
#         "Revise the draft based on the compliance feedback to create the final response. Address all identified "
#         "gaps, strengthen weak areas, ensure full compliance with requirements, and polish language and presentation.\n\n"
#     ),
#     expected_output='A final procurement response that is fully compliant, addresses all requirements, incorporates relevant past performances, and is ready for submission.',
#     agent=response_writer
# )

# # Initialize the Crew with defined agents and tasks
# procurement_response_crew = Crew(
#     name='Procurement Response Writing Crew',
#     agents=[requirements_analyzer, past_performance_specialist, sme_coordinator, 
#             response_writer, compliance_checker],
#     tasks=[analyze_requirements_task, find_relevant_performances_task, identify_sme_needs_task,
#            write_initial_response_task, verify_compliance_task, finalize_response_task],
#     process=Process.sequential,  # Ensures tasks are executed in the order they are added
#     verbose=True,
#     memory=True,
#     cache=True,
#     max_rpm=100,
#     share_crew=True
# )

# # Function to run the crew
# def generate_procurement_response(outline_content, past_performance_content):
#     # Initial input for the crew
#     initial_input = {
#         "outline_content": outline_content,
#         "past_performance_content": past_performance_content
#     }
    
#     # Run the crew with the initial input
#     result = procurement_response_crew.kickoff(inputs=initial_input)
    
#     return result

# # Example usage
# response = generate_procurement_response(outline_content, past_performance_content)

print(os.getenv("GOOGLE_API_KEY"))


# # Define the agents with updated experience and specific, low-level detail focus

# outline_parser = Agent(
#     llm=llm,
#     role="Outline Parser",
#     goal="Extract and structure the procurement outline into clear sections/volumes with specific key details.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You have 10 years of experience in procurement document analysis. "
#         "Your expertise lies in breaking down outlines into distinct, low-level components and specific instructions for each volume."
#     ),
#     allow_delegation=True
# )

# past_performance_analyzer = Agent(
#     llm=llm,
#     role="Past Performance Analyzer",
#     goal="Analyze past performance content and extract detailed, specific achievements, metrics, and success stories.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "With 10 years of experience in evaluating past performance data, you specialize in identifying and extracting compelling and precise performance details that strengthen procurement responses."
#     ),
#     allow_delegation=True
# )

# # SME Agent: Provides deep technical and specialized input when needed.
# sme_expert = Agent(
#     llm=llm,
#     role="Subject Matter Expert (SME)",
#     goal="Provide detailed, low-level solutioning and technical expertise to enhance the procurement response.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You are a specialist with 10 years of experience in procurement strategy and technical solutioning. "
#         "When consulted, you deliver actionable, specific advice that elevates the response quality to a winning level."
#     ),
#     allow_delegation=False  # SME does not further delegate; it provides direct expertise.
# )

# response_writer = Agent(
#     llm=llm,
#     role="Response Writer",
#     goal="Draft detailed, winning responses for each procurement section by integrating structured outline analysis and past performance insights. Delegate to the SME for any part requiring deeper technical solutioning.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "As a procurement response specialist with 10 years of experience, you craft responses with very specific, low-level details that directly address the procurement requirements. "
#         "Whenever deeper technical insights are needed, you consult the SME agent to ensure your response is both accurate and compelling."
#     ),
#     allow_delegation=True
# )

# # Define the tasks

# outline_parsing_task = Task(
#     description=(
#         "Task: Parse the provided outline content to extract a structured breakdown of the procurement. "
#         "Identify each section/volume and summarize key, specific details and instructions.\n"
#         "Input:\n{outline_content}"
#     ),
#     expected_output="A detailed and structured breakdown of the procurement outline, including clear sections/volumes and their specific requirements.",
#     agent=outline_parser
# )

# past_performance_task = Task(
#     description=(
#         "Task: Analyze the provided past performance content and extract detailed performance metrics and success stories. "
#         "Summarize how these achievements specifically align with the procurement requirements.\n"
#         "Input:\n{past_performance_content}"
#     ),
#     expected_output="A detailed summary of key past performance highlights and metrics, with specific insights relevant to the procurement.",
#     agent=past_performance_analyzer
# )

# # SME Agent's task for providing low-level technical expertise
# sme_consultation_task = Task(
#     description=(
#         "Task: Provide a detailed technical solution and analysis for a specific part of the procurement response. "
#         "Focus on low-level content and actionable insights. "
        
#     ),
#     expected_output="A detailed, specific technical solution with actionable insights that can be integrated into the procurement response.",
#     agent=sme_expert
# )

# response_writing_task = Task(
#     description=(
#         "Task: Using the structured outline (from Task 1) and past performance summary (from Task 2), draft a detailed, winning response for each procurement section/volume. "
#         "Incorporate specific, low-level content and consult the SME agent via the SME task when deeper technical insights are needed.\n"
#         # "Inputs:\nOutline Breakdown: {outline_breakdown}\nPast Performance Summary: {past_performance_summary}"
#     ),
#     expected_output=(
#         "A comprehensive, detailed draft response for each procurement section that integrates specific inputs from the outline, past performance data, "
#         "and enhanced technical details provided by the SME as required."
#     ),
#     agent=response_writer
# )

# # Initialize the Crew with all defined agents (including the SME) and tasks

# procurement_response_crew = Crew(
#     name="ProcurementResponseCrew",
#     agents=[outline_parser, past_performance_analyzer, sme_expert, response_writer],
#     tasks=[outline_parsing_task, past_performance_task, sme_consultation_task, response_writing_task],
#     process=Process.sequential,  # Execute tasks in the defined order
#     verbose=True,
#     # memory=True,
# #     embedder = {
# #     "provider": "google",
# #     "config": {
# #         "api_key": os.getenv("GOOGLE_API_KEY"),
# #         "model": "models/text-embedding-004"
# #     }
# # },
#     cache=True,
#     max_rpm=500,
#     share_crew=True
# )




# initial_input = {
#         "outline_content": outline_content,
#         "past_performance_content": past_performance_content
#     }

# # Run the crew with the initial input
# result = procurement_response_crew.kickoff(inputs=initial_input)
    


# # Define the Master Agent that coordinates the overall response drafting process.
# master_response_writer = Agent(
#     llm=llm,
#     role="Master Response Writer",
#     goal="Coordinate and delegate tasks to worker agents to produce a winning, highly detailed procurement response.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You have 10 years of experience in procurement response writing. As the master agent, you oversee the drafting process and delegate specific tasks "
#         "to your team of specialized workers. You ensure that every part of the response is specific, low-level, and directly addresses the procurement requirements."
#     ),
#     allow_delegation=True
# )

# # Define the Worker Agents

# outline_parser = Agent(
#     llm=llm,
#     role="Outline Parser",
#     goal="Break down and analyze the procurement outline into detailed sections.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "With 10 years of experience in analyzing procurement documents, you extract detailed, low-level instructions and components from the provided outline."
#     ),
#     allow_delegation=True
# )

# past_performance_analyzer = Agent(
#     llm=llm,
#     role="Past Performance Analyzer",
#     goal="Extract and analyze detailed performance metrics and success stories from the past performance content.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "You have 10 years of experience evaluating past performance. Your focus is on providing specific, actionable insights that support a winning response."
#     ),
#     allow_delegation=True
# )

# sme_expert = Agent(
#     llm=llm,
#     role="Subject Matter Expert (SME)",
#     goal="Provide in-depth, technical expertise and detailed, low-level solutions for complex sections of the procurement response.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "With 10 years of specialized experience in procurement strategy and technical solutioning, you deliver actionable and specific technical advice when consulted."
#     ),
#     allow_delegation=False  # SME does not delegate further.
# )

# # Define Worker Tasks

# outline_parsing_task = Task(
#     description=(
#         "Task: Parse the provided outline content to extract a detailed breakdown of procurement sections. "
#         "Identify and document key instructions and specific details for each section.\n"
#         "Input:\n{outline_content}"
#     ),
#     expected_output="A structured breakdown of the procurement outline with detailed sections and specific instructions.",
#     agent=outline_parser
# )

# past_performance_task = Task(
#     description=(
#         "Task: Analyze the provided past performance content to extract detailed metrics and success stories. "
#         "Ensure the insights are directly aligned with the procurement requirements.\n"
#         "Input:\n{past_performance_content}"
#     ),
#     expected_output="A detailed summary of performance highlights and metrics relevant to the procurement.",
#     agent=past_performance_analyzer
# )

# sme_consultation_task = Task(
#     description=(
#         "Task: Provide detailed, technical expertise on a specific complex section of the procurement response. "
#         "Focus on actionable, low-level insights that can be integrated into the final response.\n"
#     ),
#     expected_output="A detailed technical solution and actionable insights for the given query.",
#     agent=sme_expert
# )

# # Define the Master Task where the Master Agent delegates work as needed.
# master_response_writing_task = Task(
#     description=(
#         "Master Task: Using the outputs from your worker agents, create a winning, detailed procurement response. "
#         "Delegate the following as needed:\n"
#         "1. Invoke Outline Parsing Task with {outline_content} to get a breakdown of procurement sections.\n"
#         "2. Invoke Past Performance Task with {past_performance_content} to extract specific performance insights.\n"
#         "3. For any section requiring deeper technical detail, delegate to SME Consultation Task with an appropriate technical query.\n"
#         "Finally, integrate all received outputs into a comprehensive, winning response."
#     ),
#     expected_output=(
#         "A final, detailed procurement response that integrates specific insights from the outline, past performance data, "
#         "and technical expertise provided via SME consultations."
#     ),
#     agent=master_response_writer
# )

# # Initialize the Crew with the master and worker agents using a delegated process model.
# procurement_response_crew = Crew(
#     name="MasterBasedProcurementResponseCrew",
#     agents=[outline_parser, past_performance_analyzer, sme_expert],
#     tasks=[outline_parsing_task, past_performance_task, sme_consultation_task],
#     process=Process.hierarchical,  # This indicates that the master agent will delegate tasks to available worker agents as needed.
#     verbose=True,
#     memory=True,
#     cache=True,
#     max_rpm=100,
#     share_crew=True,
#     manager_agent= master_response_writer
# )



# initial_input = {
#         "outline_content": outline_content,
#         "past_performance_content": past_performance_content
#     }

# # Run the crew with the initial input
# result = procurement_response_crew.kickoff(inputs=initial_input)
    


# result.raw

# from IPython.display import Markdown
# Markdown(result.raw)

# outline_content

# past_performance_content

# # Define the Manager Agent to oversee the entire response generation process.
# manager_agent = Agent(
#     llm=llm,
#     role="Response Manager",
#     goal=(
#         "Oversee and ensure that the final winning response strictly follows the provided outline. "
#         "Ensure that the response is structured volume/section-wise as per the outline. "
#         "Delegate to the Past Performance Analyzer for performance data and the SME for detailed technical insights whenever needed."
#     ),
#     verbose=True,
#     memory=True,
#     backstory=(
#         "With 10 years of experience in managing procurement responses, you ensure that the final submission is specific, detailed, "
#         "and strictly adheres to the provided outline structure."
#     ),
#     allow_delegation=True
# )

# # Past Performance Analyzer: Provides detailed, specific achievements and metrics from past performance data.
# past_performance_analyzer = Agent(
#     llm=llm,
#     role="Past Performance Analyzer",
#     goal="Analyze the provided past performance content and supply specific metrics, achievements, and success stories for integration into the response.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "With 10 years of experience in evaluating past performance data, you excel at extracting actionable insights that strengthen procurement responses."
#     ),
#     allow_delegation=True
# )

# # SME: Supplies deep technical expertise and low-level solutioning when required.
# sme_expert = Agent(
#     llm=llm,
#     role="Subject Matter Expert (SME)",
#     goal="Provide detailed technical solutions and actionable insights to enhance the winning response.",
#     verbose=True,
#     memory=True,
#     backstory=(
#         "A specialist with 10 years of experience in procurement strategy and technical solutioning, you deliver precise, actionable technical guidance "
#         "to ensure every response detail is accurate."
#     ),
#     allow_delegation=False  # SME provides direct input without further delegation.
# )

# # Response Writer: Uses the provided outline and past performance content as the basis for drafting the winning response.
# # The response writer can call on the SME or Past Performance Analyzer as needed.
# response_writer = Agent(
#     llm=llm,
#     role="Response Writer",
#     goal=(
#         "Draft a detailed and winning procurement response using the provided outline as a blueprint. "
#         "Fill in the placeholders and ensure the final response is organized strictly according to the outline's volume/section breakdown. "
#         "When additional technical detail or specific past performance data is required, delegate to the Past Performance Analyzer or the SME as needed."
#     ),
#     verbose=True,
#     memory=True,
#     backstory=(
#         "With 10 years of experience in drafting procurement responses, you craft detailed and specific submissions that strictly adhere to the provided outline. "
#         "Your writing process includes organizing content by volume or section as specified, and integrating past performance metrics and technical details via on-demand consultations."
#     ),
#     allow_delegation=True
# )

# # Define the main task where the Response Writer creates the winning response.
# response_writing_task = Task(
#     description=(
#         "Task: Using the provided outline and past performance content, draft a comprehensive and winning procurement response. "
#         "The response must follow the outline exactly—organized by volumes, sections, or subsections as defined in the outline. "
#         "Fill in all placeholders accordingly. For sections that require more detailed past performance data or technical insights, "
#         "delegate to the Past Performance Analyzer or SME respectively."
#     ),
#     expected_output=(
#         "A detailed, specific procurement response that mirrors the provided outline, with each volume or section addressed explicitly, "
#         "integrates precise past performance references, and includes technical insights where required."
#     ),
#     agent=response_writer
# )

# # Define the Past Performance Task for on-demand delegation.
# past_performance_task = Task(
#     description=(
#         "Task: Analyze the provided past performance content and extract detailed performance metrics, achievements, and success stories. "
#         "Summarize how these specific achievements align with the procurement requirements and indicate where they should be referenced in the response."
#     ),
#     expected_output=(
#         "A summary of key past performance details, including metrics and success stories, with clear indications of where they fit into the final response."
#     ),
#     agent=past_performance_analyzer
# )

# # Define the SME Consultation Task for on-demand delegation.
# sme_consultation_task = Task(
#     description=(
#         "Task: Provide a detailed technical solution and analysis for a specific section of the procurement response. "
#         "Focus on low-level content and actionable insights that enhance the technical credibility of the submission."
#     ),
#     expected_output=(
#         "A detailed technical solution with actionable insights that can be directly integrated into the relevant section of the procurement response."
#     ),
#     agent=sme_expert
# )

# # Assemble the hierarchical crew with the Manager overseeing the Response Writer, Past Performance Analyzer, and SME.
# hierarchical_procurement_crew = Crew(
#     name="HierarchicalProcurementResponseCrew",
#     manager_agent=manager_agent,  # Manager oversees and coordinates the process.
#     agents=[response_writer, past_performance_analyzer, sme_expert],
#     tasks=[response_writing_task, past_performance_task, sme_consultation_task],
#     process=Process.hierarchical,  # Hierarchical process where the Manager delegates tasks as needed.
#     verbose=True,
#     cache=True,
#     max_rpm=500,
#     share_crew=True
# )

# # Initial input includes the outline (already structured into volumes/sections) and the past performance content.
# initial_input = {
#     "outline_content": outline_content,
#     "past_performance_content": past_performance_content
# }

# # Kickoff the hierarchical crew using the provided inputs.
# result = hierarchical_procurement_crew.kickoff(inputs=initial_input)






# Markdown(result.raw)

# create_markdown_file(generated_response_file_name, result.raw)