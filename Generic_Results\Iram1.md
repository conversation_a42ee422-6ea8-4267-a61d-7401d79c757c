START pre-toc:Content Before Table of Contents

**Section Title:** Content Before Table of Contents
**TOC Reference:** Appears in TOC: No

**Generated Content:**

This proposal includes data that shall not be disclosed outside the Government and shall not be duplicated, used or disclosed in whole or in part for any purpose other than to evaluate this proposal. If, however, a Contract is awarded to this Offeror as a result of or in connection with the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting Contract. This restriction does not limit the Government’s right to use information contained in this data if it is obtained from another source without restriction. The data subject to the restriction is contained in all sheets of this proposal. // Source: RFP Outline

**Office of the Comptroller of the Currency (OCC)**
**Cyber Security Office (CSO)**
**Cybersecurity Assessment and Compliance (CA&C)**
**Solicitation Number: 2031JW22Q00022**
**Due Date: 10:00 AM (EST), October 29, 2021**
**Date of Submission: October 29, 2021**
**Agency: Office of the Comptroller of the Currency (OCC)**
**Subject: TENANT_NAME Response to Cybersecurity Assessment and Compliance (CA&C)** // Source: RFP Outline

TENANT_NAME is pleased to submit this comprehensive response to the Office of the Comptroller of the Currency (OCC) for the Cybersecurity Assessment and Compliance (CA&C) requirement, RFQ number 2031JW22Q00022. We are driven by a commitment to solving complex challenges and ensuring the sustained success of our clients through dedicated project leadership across diverse industries. Our extensive past performance and strong reputation for excellence with our clients underscore our consistent delivery of low-risk, high-quality, and value-added strategic support services. // Source: RFP Outline

TENANT_NAME has a proven track record of providing Cybersecurity Assessment and Compliance services for many years to a broad client base within both federal government and commercial sectors. Our experience encompasses projects of similar size and scope to the work outlined for the OCC program. Our deep subject matter expertise in Cybersecurity Assessment and Compliance, combined with our experience in delivering innovative services and proven program management skills, positions us to provide the highest quality support. // Source: RFP Outline

We are enthusiastic about the opportunity to collaborate with the OCC and are eager to demonstrate the immediate positive impact TENANT_NAME can have on your organization. We acknowledge receipt of the RFQ and all amendments, and we take no exception to the stated terms and conditions. Our company information, including details for any teaming partners, is provided herein. // Source: RFP Outline

TENANT_NAME is personally committed to the success of this program and fully agrees with all terms, conditions, and provisions. We are prepared to furnish all items included in the solicitation. With our dynamic leadership, expert supervision, and highly capable personnel, we are confident that TENANT_NAME is ideally suited to fulfill your needs. Should you have any questions or require additional information, please do not hesitate to contact our primary point of contact. // Source: RFP Outline

Respectfully,

[Authorized Signatory Name]
[Title]
TENANT_NAME

---
**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: None
- Only Mentioned in Summary, Not Detailed: Solicitation Number, Agency, Due Date, CA&C (confirmed by summary, detailed in outline)
- No Data Found - Will Use General Response: None
- No Data Found - Will State Not Available: None
- Duplication Check: Pass (Consolidated repeated information from outline)
- Sequence Check: Pass (Followed the logical flow of the outline for a cover page/introductory section)END pre-toc:Content Before Table of Contents



START toc:Table of Contents

**Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: Table of Contents structure (all entries).
- Only Mentioned in Summary, Not Detailed: None.
- No Data Found - Will Use General Response: None.
- No Data Found - Will State Not Available: None.
- Duplication Check: Pass
- Sequence Check: Pass

---

**Section Title:** Table of Contents
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

TENANT_NAME is pleased to present the comprehensive Table of Contents for our response to RFQ 2031JW22Q00022, outlining the structure and organization of our proposal. This detailed roadmap ensures easy navigation and highlights our thorough approach to addressing all solicitation requirements.

**Table of Contents**

1 Assumptions
    1.1 Technical Assumptions
    1.2 Price Assumptions
2 SF 1449
3 Acknowledgment of Solicitation Amendments
4 Completion of Representations:
5 Attestation
6 Conflict of Interest Mitigation Plan
7 GSA Federal Supply Schedule (FSS)
8 Technical/Management Approach (No more than 15 pages)
    8.1 Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing
    8.2 Methodology for assigning risk ratings to weaknesses discovered during the assessment process
    8.3 Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)
    8.4 Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool
    8.5 Process for automating assessments
    8.6 Methodology for tracking, reporting and completing all work identified in the PWS
    8.7 Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section
9 Key Personnel Resumes (No more than 3 pages per resume)
    9.1 Project Manager
    9.2 Cybersecurity Assurance & Compliance Program Support Lead
    9.3 Security & Privacy Control Assessor Lead
    9.4 Quality Assurance Lead
    9.5 Tier 2 eGRC Specialist
    9.6 Automated Assessment Developer
    9.7 IT Security Subject Matter Expert
10 Past Performance

// Source: knowledge_base (Table of Contents structure)END toc:Table of Contents



START 1-7:Administrative and Compliance Responses

**Section Title:** Administrative and Compliance Responses
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

**2 SF 1449**
Content not available in the provided knowledge base. This section requires the completed and signed SF 1449 form to be inserted by the Quoter.

**3 Acknowledgment of Solicitation Amendments**
TENANT_NAME acknowledges receipt of the Request for Quotation (RFQ) and all associated amendments, taking no exception to the stated terms and conditions. // Source: Team Greenbrier Response to Cybersecurity Assessment and Compliance (CA&C) cover letter

**4 Completion of Representations**
Content not available in the provided knowledge base. This section requires the completion of specific representations and certifications, including OCC provision 1052.209-8001, Conflict of Interest Disclosure and Certification (Feb 2014).

**5 Attestation**
TENANT_NAME attests that all proposed contractor personnel meet or exceed the Contractor Qualifications and Key Personnel requirements as outlined in Section II, “Qualifications for Tasks/Key Personnel” of the Statement of Work. We are fully committed to the success of this program and agree to all terms, conditions, and provisions, prepared to furnish all items included in this solicitation with our highly capable personnel. // Source: RFP Outline; Team Greenbrier Response to Cybersecurity Assessment and Compliance (CA&C) cover letter

**6 Conflict of Interest Mitigation Plan**
Content not available in the provided knowledge base. This section requires a statement regarding any actual or potential conflicts of interest and, if applicable, a corresponding mitigation plan.

**7 GSA Federal Supply Schedule (FSS)**
Content not available in the provided knowledge base. This section requires explicit confirmation that all proposed services fall under TENANT_NAME's GSA Federal Supply Schedule (FSS), along with details of any teaming arrangements and associated GSA FSS numbers for affected CLINs.END 1-7:Administrative and Compliance Responses



START 8:Technical/Management Approach

**Section Title:** Technical/Management Approach
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

TENANT_NAME is committed to providing comprehensive and effective cybersecurity assessment and compliance support to the Office of the Comptroller of the Currency (OCC). Our technical and management approach is designed to ensure robust security posture, maintain compliance with federal regulations, and deliver high-quality, value-added services. This approach leverages our extensive experience, proven methodologies, and skilled personnel to meet and exceed the requirements outlined in the Performance Work Statement (PWS).

### 8.1 Methodology/process for testing web applications, financial systems, network and infrastructure devices and end-user devices, including a detailed process for manual and automated testing

TENANT_NAME employs a comprehensive methodology for testing web applications, financial systems, network and infrastructure devices, and end-user devices, integrating both manual and automated testing processes. Our approach is rooted in established security control assessment (SCA) practices, ensuring thorough evaluation and identification of vulnerabilities. We have extensive experience performing detailed, full-scope technical security control testing across various system types. // Source: Security Control Assessments project, Key Personnel descriptions

Our detailed testing process includes:
*   **Planning and Scope Definition:** We begin by developing a Security Assessment Plan (SAP) that clearly defines the scope of testing, identifies the specific controls to be tested, and outlines the execution method. This plan considers the system type, location, and appropriate management controls. // Source: Security Control Assessments project
*   **Tool-Assisted Automated Testing:** We leverage industry-leading cybersecurity tools for automated assessments, including Qualys Enterprise, Nessus, and HCL AppScan for vulnerability scanning and web application security testing. For more advanced and specialized testing, we utilize technical outputs from the Kali Linux suite. These tools enable efficient and broad coverage for identifying common vulnerabilities and misconfigurations across diverse environments. // Source: Key Personnel descriptions
*   **Manual Verification and In-depth Analysis:** Automated scans are complemented by manual testing procedures to validate findings, reduce false positives, and uncover complex vulnerabilities that automated tools might miss. Our assessors perform in-depth analysis, including configuration reviews for major IT products, to ensure adherence to approved organizational benchmarks. We also conduct interviews and examinations as part of our assessment test types, as identified in NIST SP 800-53A. // Source: Security Control Assessments project
*   **Component-Specific Assessment:** Our methodology ensures that all software and hardware components within the defined system boundary are assessed. For systems with identically configured devices, we employ representative sampling while providing clear rationale for components not directly tested. // Source: Security Control Assessments project
*   **Reporting and Remediation Recommendations:** All assessment results are meticulously documented in a Security Assessment Report (SAR), detailing findings, identified weaknesses, and providing actionable recommendations for corrective actions. // Source: Security Control Assessments project

### 8.2 Methodology for assigning risk ratings to weaknesses discovered during the assessment process

TENANT_NAME utilizes a structured methodology for assigning risk ratings to weaknesses identified during the assessment process, ensuring a consistent and objective evaluation of potential impact. Our approach aligns with federal guidelines and best practices for risk management.

Our methodology includes:
*   **Severity and Criticality Assessment:** We assess the severity and criticality of each discovered weakness or deficiency based on prioritized levels reported by NIST. This involves evaluating the potential impact on confidentiality, integrity, and availability of information systems and their operating environment. // Source: Security Control Assessments project
*   **Corrective Action Recommendations:** For every identified vulnerability, we provide clear and actionable corrective actions to address the weakness. // Source: Security Control Assessments project
*   **Documentation in POA&M:** All findings, including assigned risk ratings and recommended corrective actions, are meticulously documented in a Plan of Action and Milestones (POA&M) import template. This ensures a standardized and auditable record for tracking remediation efforts. // Source: Security Control Assessments project

### 8.3 Process for audit preparation and tracking, distributing, and responding to data call requests associated with annual external audits (FISCAM, FISMA and A-123)

TENANT_NAME understands the critical importance of robust audit preparation and efficient management of data call requests for annual external audits such as FISCAM, FISMA, and A-123. Our process is designed to streamline these activities, minimize disruption, and ensure timely and accurate responses.

Our process typically includes:
*   **Proactive Audit Readiness:** We establish a continuous audit readiness posture by maintaining comprehensive documentation, ensuring controls are operating effectively, and regularly reviewing compliance artifacts. This includes ongoing compliance oversight activities. // Source: RFP Outline; RFP Summary
*   **Centralized Data Call Management:** We implement a centralized system for tracking, distributing, and managing all data call requests. This system ensures that requests are promptly assigned to the appropriate subject matter experts and that deadlines are met.
*   **Stakeholder Coordination:** We facilitate seamless coordination among all relevant stakeholders, including system owners, technical teams, and compliance officers, to gather necessary information and evidence for audit responses.
*   **Quality Assurance and Review:** All responses to data call requests undergo a rigorous quality assurance review process to ensure accuracy, completeness, and alignment with audit requirements before submission.
*   **Lessons Learned Integration:** Following each audit cycle, we conduct a lessons learned review to identify areas for improvement in our audit preparation and response processes, continuously enhancing efficiency and effectiveness. // Source: Generic proposal guidance

### 8.4 Process for integrating the Risk Management Framework (RMF) – Monitor step with an eGRC tool

TENANT_NAME possesses extensive experience in integrating the Risk Management Framework (RMF) Monitor step with eGRC tools, particularly ServiceNow GRC, to enable continuous monitoring and real-time visibility into an organization's security posture. Our approach ensures that security controls are continuously assessed, vulnerabilities are tracked, and compliance is maintained throughout the system lifecycle.

Our process for RMF Monitor step integration with eGRC tools includes:
*   **eGRC Tool Expertise:** We leverage our deep expertise with various eGRC platforms, including ServiceNow GRC, RSA Archer, CSAM, and Xacta, to configure and optimize them for RMF monitoring activities. Our personnel have significant experience in GRC implementation and development, including advanced scripting and customization within ServiceNow. // Source: Key Personnel descriptions; Security Control Assessments project
*   **Continuous Monitoring Plan Development:** We develop and implement a Continuous Monitoring SCA Plan and Schedule that outlines required activities and outputs aligned with RMF tasks. This plan ensures that ongoing assessments are systematically performed. // Source: Security Control Assessments project
*   **Automated Data Collection and Aggregation:** We configure the eGRC tool to automatically collect and aggregate security data from various sources, including vulnerability scanners, configuration management databases, and system logs. This automation streamlines the data collection process for continuous assessment.
*   **Control Assessment and Status Tracking:** The eGRC tool is utilized to track the status of security controls, document assessment results, and manage Plan of Action and Milestones (POA&Ms). This provides a centralized repository for all RMF-related documentation and activities. We review all required supporting artifacts of record in the GRC tool to support the security state of the system being assessed. // Source: Security Control Assessments project
*   **Reporting and Dashboards:** We develop customized dashboards and reports within the eGRC tool to provide real-time visibility into the organization's risk posture, control effectiveness, and compliance status. This supports informed decision-making and facilitates communication with stakeholders. We perform all required communications and reporting activities as required by RMF Tasks. // Source: Security Control Assessments project

### 8.5 Process for automating assessments

TENANT_NAME is adept at automating security assessments to enhance efficiency, consistency, and coverage. Our process for automating assessments leverages specialized tools and integration capabilities to streamline the identification and reporting of vulnerabilities.

Our process includes:
*   **Tool Integration:** We integrate leading cybersecurity tools such as Qualys, RSA Archer, and Splunk with eGRC platforms like ServiceNow GRC. This integration allows for automated data flow and centralized management of assessment results. // Source: Key Personnel descriptions
*   **Automated Scan Execution:** We configure and schedule automated vulnerability scans and security configuration assessments using tools like Qualys Enterprise and Nessus. These scans are designed to identify common vulnerabilities, misconfigurations, and compliance deviations across various system types. // Source: Key Personnel descriptions
*   **Custom Scripting and Development:** Our Automated Assessment Developers possess expertise in developing custom scripts and solutions to automate assessment tasks, data parsing, and reporting. This includes advanced ServiceNow scripting for data tables, fields, and user interface actions. // Source: Key Personnel descriptions
*   **Automated Reporting and Remediation Tracking:** Assessment results are automatically fed into the eGRC tool, enabling automated generation of reports and tracking of remediation efforts through POA&Ms. This reduces manual effort and accelerates the remediation lifecycle.

### 8.6 Methodology for tracking, reporting and completing all work identified in the PWS

TENANT_NAME employs a robust and transparent methodology for tracking, reporting, and ensuring the completion of all work identified in the Performance Work Statement (PWS). Our approach emphasizes proactive project oversight, clear communication, and continuous monitoring to deliver high-quality results.

Our methodology includes:
*   **Centralized Project Management:** We provide comprehensive project oversight by prioritizing, planning, tracking, and reporting on all project activities. This ensures the efficient implementation of tasks and deliverables. // Source: FSS Program Management Support project
*   **Detailed Project Scheduling:** We develop and maintain a master project schedule and timeline using tools such as MS Project. This schedule outlines key components, milestones, and deliverables, providing a clear roadmap for project execution. // Source: FSS Program Management Support project
*   **Regular Status Reporting:** We provide consistent visibility and reporting on the status and progress of all activities. This includes weekly communications, action item update reports, and bi-weekly activity reports (BWARs) that detail accomplishments, future activities, risks, and projected completion dates. Reports are tailored to provide concise formats, including dashboards and PowerPoint presentations. // Source: FSS Program Management Support project
*   **Performance Metrics and Governance:** We monitor performance metrics to measure implementation progress in a graphical format, continuously revising them as needed. We also provide ongoing program governance support, assessing and recommending prioritization of tasks to optimize workload management. // Source: FSS Program Management Support project
*   **Proactive Risk and Issue Management:** We utilize project management tools (MS Project, SharePoint, MS Excel, MS Word, Adobe Pro) and techniques to enhance visibility and escalate risks and issues to leadership as needed, ensuring timely resolution. // Source: FSS Program Management Support project
*   **Quality Assurance and Deliverable Review:** All work products and deliverables undergo thorough review and acceptance processes to ensure they meet quality standards and client requirements.

### 8.7 Staffing plan and approach for obtaining and maintaining individuals with the qualifications listed in the Key Personnel section

Content not available in the provided knowledge base. This section would typically detail TENANT_NAME's recruitment strategies, talent acquisition processes, employee retention programs, professional development and training initiatives, and succession planning to ensure a continuous supply of qualified personnel, particularly those meeting the specific criteria outlined for Key Personnel roles.END 8:Technical/Management Approach



START 9:Key Personnel Resumes

**Section Title:** Key Personnel Resumes
**TOC Reference:** Appears in TOC: Yes

**Agentic Validation Summary (Pre-Generation Checks):**
- Direct Matches from Knowledge Base: None
- Only Mentioned in Summary, Not Detailed: Key Personnel roles (Project Manager, Cybersecurity Assurance & Compliance Program Support Lead, Security & Privacy Control Assessor Lead, Quality Assurance Lead, Tier 2 eGRC Specialist, Automated Assessment Developer, IT Security Subject Matter Expert)
- No Data Found - Will Use General Response: None (not applicable for this section type)
- No Data Found - Will State Not Available: All specific resume content and letters of commitment.
- Duplication Check: Pass
- Sequence Check: Pass

**Generated Content:**

TENANT_NAME is committed to providing highly qualified and experienced personnel to fulfill the requirements of the Cybersecurity Assessment and Compliance (CA&C) program. As requested, we will submit comprehensive resumes for each identified Key Personnel, ensuring adherence to the specified page limit of no more than three (3) pages per resume. Each proposed key personnel will also be accompanied by a letter of commitment, affirming their availability and dedication to this critical engagement. // Source: RFP Outline

**Project Manager**
Our proposed Project Manager will possess the requisite qualifications to lead this effort effectively. This includes a minimum of eight (8) years of experience in program and project management, specifically supporting information security or cybersecurity projects within the federal government. The candidate will hold a bachelor's degree from an accredited college and a Project Management Professional (PMP) certification or an equivalent credential approved by the OCC COR. Furthermore, the Project Manager will demonstrate hands-on proficiency with Microsoft Office Project and possess essential experience with the NIST Risk Management Framework, Governance, Risk & Compliance (GRC) tools such as ServiceNow GRC, RSA Archer, CSAM, and Xacta, and other Information Assurance capabilities. Required certifications include Certified Information Security Auditor (CISA), Certified Authorization Professional (CAP), Certified Information Systems Security Professional (CISSP), or Certified Information Security Manager (CISM). // Source: RFP Outline

**Cybersecurity Assurance & Compliance Program Support Lead**
The Cybersecurity Assurance & Compliance Program Support Lead will bring a minimum of three (3) years of experience managing security compliance program support teams. This individual will have at least six (6) years of experience, within the last five years, in developing RMF documentation, including Standard Operating Procedures (SOPs), system security plans (SSPs), program plans, processes, and workflows. A total of eight (8) years of Information Security experience is required, along with at least two (2) years of experience utilizing eGRC tools. The candidate will hold one of the following certifications: Certified Information Systems Security Professional (CISSP), Certified Information Security Auditor (CISA), Certified Authorization Professional (CAP), Certified in Risk and Information Systems Control (CRISC), or Certified Information Security Manager (CISM). Experience with the ServiceNow GRC CAM tool is preferred. // Source: RFP Outline

**Security & Privacy Control Assessor Lead**
Our Security & Privacy Control Assessor Lead will have a minimum of three (3) years of experience managing security assessment teams and six (6) years of experience performing detailed, full-scope technical security control testing for various component types, including the development of security and privacy assessment plans. A total of eight (8) years of Information Security experience is required, with at least two (2) years of experience using eGRC tools, preferably ServiceNow. The candidate will be proficient with tools such as Qualys Enterprise, Archer, Nessus, HCL AppScan, and technical outputs of the Kali Linux suite. Advanced knowledge of security configurations for relevant component types is also required. Necessary certifications include Certified Information Systems Security Professional (CISSP), Certified Ethical Hacker (CEH), Certified Risk and Information Systems Control (CRISC), or Certified Information Security Auditor (CISA). // Source: RFP Outline

**Quality Assurance Lead**
The Quality Assurance (QA) Lead will possess at least three (3) years of experience managing technical security QA teams and/or SA&A Package Independent Validation & Verification (IV&V). This role requires six (6) years of experience in developing RMF documentation and six (6) years of experience conducting security and privacy control assessments. A total of eight (8) years of Information Security experience is mandated, along with two (2) years of experience with eGRC tools, with a preference for ServiceNow GRC tool suite, including CAM. The candidate will be certified in Certified Information Systems Security Professional (CISSP), Certified Risk and Information Systems Control (CRISC), or Certified Information Security Auditor (CISA). // Source: RFP Outline

**Tier 2 eGRC Specialist**
The Tier 2 eGRC Specialist will have a minimum of two (2) years of experience, within the last three years, in developing advanced custom ServiceNow script includes, user interface actions, user interface policies, access control lists, client scripts, scheduled jobs, data tables, and data fields. At least three (3) years of Integrated Risk Management (IRM)/GRC implementation experience in the last four years is required, with GRC CAM experience and ServiceNow Certified Risk & Compliance implementation specialist certification preferred. The specialist will also have at least two (2) years of experience, within the last three years, configuring and customizing the ITSM suite, IT Operations Management suite, and NOW Platform Capabilities, as well as experience with technical components like LDAP, Web Services, REST, SOAP, APIs, XML, and JavaScript. Two (2) years of experience with ITILv3 Service Management processes in the last three years is also required. Furthermore, at least three (3) years of Administrator/Developer experience in the last four years is necessary, with GRC ServiceNow Certified Developer preferred, and at least two (2) years of ServiceNow Implementation Specialist experience in the last three years, with Certified ServiceNow Implementation Specialist preferred. // Source: RFP Outline

**Automated Assessment Developer**
Our Automated Assessment Developer will have a minimum of five (5) years of experience as a software developer. Three (3) years of experience integrating cybersecurity tools, including Qualys, RSA Archer, and Splunk with ServiceNow GRC and ServiceNow formats and languages, is preferred. A Bachelor of Science degree in Computer Science is required for this position. // Source: RFP Outline

**IT Security Subject Matter Expert**
The IT Security Subject Matter Expert (SME) will bring extensive experience to areas requiring elevated technical security skills, such as risk analysis of cutting-edge technology or high-visibility projects. This role requires ten (10) years of IT security experience and five (5) years of experience performing detailed, full-scope technical control testing for specified component types, including the development of security assessment plans. In-depth knowledge and experience in implementing and using GRC platforms are required, with a preference for ServiceNow GRC tool suite, including CAM. The SME will have hands-on experience with at least four (4) of the following technology products: Forescout CounterAct, ArcSight, HCL BigFix, Sailpoint, CyberArk, RES, and Splunk. Extensive knowledge of security configurations for relevant component types is also required. A Bachelor of Science degree in Computer Science, Cybersecurity, or Information Systems is mandatory, along with a Certified Information Systems Security Professional (CISSP) certification. A Certified Cloud Security Professional (CCSP) is preferred. // Source: RFP Outline

[Insert Resumes Here - Content not available in the provided knowledge base. Actual resumes will be provided in the final submission.]END 9:Key Personnel Resumes



START 10:Past Performance

**Section Title:** Past Performance
**TOC Reference:** Appears in TOC: Yes

**Generated Content:**

TENANT_NAME, as part of Team Greenbrier, possesses extensive experience in providing Cybersecurity Assessment and Compliance services to a diverse client base within both the federal government and commercial sectors. Our past performance demonstrates our capability to successfully execute projects similar in size and scope to the requirements of the Office of the Comptroller of the Currency (OCC) program. Our deep subject matter expertise in Cybersecurity Assessment, Compliance, and proven program management skills enable us to deliver high-quality, value-added strategic support services. // Source: RFP Summary

We are pleased to present the following relevant past performance examples that highlight our capabilities in supporting critical cybersecurity and compliance initiatives:

**Project 1: FSS Program Management Support**
*   **Contracting Agency:** Department of Veterans Affairs // Source: knowledge_base
*   **Contract Name:** FSS Program Management Support (HMS Technologies) // Source: knowledge_base
*   **Contract Type:** Firm Fixed Price (FFP) // Source: knowledge_base
*   **Place of Performance:** Remote // Source: knowledge_base
*   **Role:** Subcontractor // Source: knowledge_base
*   **Description of Services:** TENANT_NAME provided comprehensive project oversight to the VA OIS FSS (now ITOPS ESO), encompassing prioritization, planning, tracking, and reporting to ensure the successful implementation of OIS FSS projects, SharePoint Support, and Governance. This included managing requirements for the OIS Business Office support program and providing support for the Continuous Readiness Information Security Protection (CRISP) project. We developed and maintained an updated FSS Master Project Schedule and timeline for key components, milestones, and deliverables, ensuring visibility and reporting on the status and progress of CRISP sustainment, existing security practices, Plan of Action and Milestones (POA&M) remediation, action items, and training standardization. // Source: knowledge_base
*   **Specific Task Relevance:**
    *   **FSS SharePoint Support:** We conducted analyses of SharePoint workload data to identify and prioritize improvements in FSS business operations, including process changes, application of pertinent technology like SharePoint tracking, and enhancing the ability to utilize SharePoint applications for improved business operations. This involved upgrading and enhancing the ISO Support Request Program and delivering tracking and reporting SharePoint pages. // Source: knowledge_base
    *   **FSS CRISP Program Support:** We developed and maintained an updated FSS Master Project Schedule and timeline for the CRISP project, utilizing MS Project to track tasks and sub-elements. Our team coordinated project activities among regional directors, Network ISOs (NISOs), and Subject Matter Experts (SMEs) related to CRISP, providing regular visibility and reporting on sustainment activities, POA&M remediation, and standardization of training. We also provided weekly and ad-hoc communications to FSS leadership on status, issues, risks, and accomplishments. // Source: knowledge_base
    *   **Governance Support:** TENANT_NAME provided ongoing program governance support, including assessing and recommending project prioritization, assisting in creating FSS program strategy, mission, and goals, and monitoring performance metrics. We ensured project governance aligned with FSS mission and goals, developing and maintaining supporting templates, processes, and metrics. // Source: knowledge_base
    *   **FSS Requirements Elaboration Planning Support:** We assisted in updating the annual FSS Requirements Plan, maintaining it quarterly to reflect updates in stakeholder identification and program interests. We also coordinated weekly status meetings for Bi-Weekly Activity Reports (BWAR) and provided communication support for the FSS Master Schedule, Timeline, and Milestones. // Source: knowledge_base

**Project 2: Security Control Assessments**
*   **Contracting Agency:** Department of Veterans Affairs // Source: knowledge_base
*   **Contract Name:** Security Control Assessments // Source: knowledge_base
*   **Contract Type:** Firm Fixed Price (FFP) // Source: knowledge_base
*   **Place of Performance:** Remote Locations // Source: knowledge_base
*   **Role:** Prime Contractor // Source: knowledge_base
*   **Period of Performance:** 12 months from date of award, with four 12-month option periods. // Source: knowledge_base
*   **Description of Services:** TENANT_NAME performed comprehensive Security Control Assessments (SCAs) for the Department of Veterans Affairs (VA) Office of Information & Technology (OI&T), Office of Information Security (OIS). This project addressed identified IT Security Controls Material Weaknesses from annual FISCAM and CFSA audits, ensuring continued compliance and oversight of security control objectives to meet federal regulatory requirements. Our work involved assessing the extent to which required NIST information security controls were correctly implemented, operating as intended, and producing desired outcomes within VA’s information systems. // Source: knowledge_base
*   **Specific Task Relevance:**
    *   **Contractor Independent Assessment:** We performed SCAs for approximately 375 VA information systems (FIPS High, Moderate, and unclassified systems), assessing approximately 125 systems annually due to the three-year expiration cycle. We provided assessments of weakness criticality based on NIST-prioritized levels and recommended corrective actions. Our auditable methodologies ensured all SCA tests were developed, executed, and reported consistent with VA Policy, VA’s Assessment and Authorization (A&A) Standard Operating Procedure (SOP), and SCA Guidelines, in accordance with the Risk Management Framework (RMF). // Source: knowledge_base
    *   **Security Control Assessment Preparation:** Our team reviewed supporting artifacts in the Governance, Risk, and Compliance tool, collaborated with System Owners (SOs) to develop System Rules of Engagement (ROE) Agreements, and developed and submitted SCA Test Plans. These plans detailed the scope, controls to be tested, execution methods, key resources, roles, personnel, and test procedures. // Source: knowledge_base
    *   **Security Control Assessment Execution:** We performed SCAs according to the established processes and procedures outlined in the SAP, including pre-site meetings, daily status updates, weekly status reports, and SCA out-brief meetings. // Source: knowledge_base
    *   **Security Assessment Report:** We developed comprehensive Security Assessment Reports for each SCA, documenting assessment objectives, test types, assessed components, step-by-step procedures, control assessment results, evidence of testing, configuration settings, and findings. All findings were documented in the OCS POA&M Import Template, providing unbiased and factual results. // Source: knowledge_base
    *   **Ongoing Security Control Assessments:** We developed a Continuous Monitoring SCA Plan and Schedule, performing annual SCAs and all required communications and reporting activities as mandated by RMF Tasks. // Source: knowledge_base

In accordance with the solicitation requirements, TENANT_NAME will ensure that our references complete and return three (3) Past Performance Questionnaire Surveys (Attachment 3) directly to the OCC Contract Specialist by the specified deadline. We understand that completed survey forms may not be submitted by the Quoter. We also acknowledge that the OCC may consider other relevant information, including data from the Contractor Performance Assessment Reporting System (CPAR), in evaluating our past performance. // Source: RFP OutlineEND 10:Past Performance



