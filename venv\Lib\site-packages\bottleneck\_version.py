
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-05-12T23:26:03+0200",
 "dirty": false,
 "error": null,
 "full-revisionid": "7a0110dff7e14012fb2924ba50668b2f1f83008c",
 "version": "1.5.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
